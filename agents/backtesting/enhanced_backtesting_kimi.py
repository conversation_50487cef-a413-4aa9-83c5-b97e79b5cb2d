#!/usr/bin/env python3
"""
Continuous Pipeline Backtesting System with Worker-Based Parallelism
Uses continuous worker pipeline instead of batch processing
"""

import os
import sys
import asyncio
import logging
import time
import psutil
import gc
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor
import threading
import queue
from collections import deque

import polars as pl
import numpy as np
import torch

# GPU memory management
GPU_MEMORY_THRESHOLD = 0.8  # Use max 80% of VRAM
MAX_CONCURRENT_WORKERS = 10  # Number of parallel workers
MEMORY_CHECK_INTERVAL = 50   # Check every 50 files
STATS_UPDATE_INTERVAL = 20   # Update stats every 20 completed files

# Ensure the project root is in the Python path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Set NUMEXPR_MAX_THREADS to avoid excessive thread creation
os.environ['NUMEXPR_MAX_THREADS'] = '8'

# Enable Polars optimizations
try:
    pl.enable_string_cache()
    os.environ['POLARS_MAX_THREADS'] = str(max(1, os.cpu_count() // 2))  # Reserve half cores for async
    logger = logging.getLogger(__name__)
    logger.debug("Polars optimizations enabled")
except Exception as e:
    pass

from agents.backtesting.kimi import (
    config,
    file_io
)
from utils.real_gpu_accelerator import real_gpu_accelerator
from agents.signal_generation.signal_agent import SignalAgent

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

signal_agent = SignalAgent(str(config.STRATEGIES_FILE))

class MemoryMonitor:
    """Monitor system and GPU memory usage"""
    
    @staticmethod
    def get_gpu_memory_usage():
        """Get GPU memory usage percentage"""
        try:
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated()
                cached = torch.cuda.memory_reserved()
                total = torch.cuda.get_device_properties(0).total_memory
                usage_pct = (allocated + cached) / total
                return usage_pct, allocated, cached, total
            return 0.0, 0, 0, 0
        except Exception:
            return 0.0, 0, 0, 0
    
    @staticmethod
    def get_system_memory_usage():
        """Get system memory usage percentage"""
        memory = psutil.virtual_memory()
        return memory.percent / 100.0
    
    @staticmethod
    def should_throttle():
        """Check if we should throttle processing due to memory constraints"""
        gpu_usage, _, _, _ = MemoryMonitor.get_gpu_memory_usage()
        sys_usage = MemoryMonitor.get_system_memory_usage()
        
        return gpu_usage > GPU_MEMORY_THRESHOLD or sys_usage > 0.85

class PerformanceTracker:
    """Track performance metrics across all workers"""
    
    def __init__(self, total_files: int):
        self.total_files = total_files
        self.start_time = time.time()
        self.completed_files = 0
        self.total_trades = 0
        self.processing_times = deque(maxlen=50)  # Keep last 50 processing times
        self.lock = threading.Lock()
    
    def update_completion(self, processing_time: float, trades_count: int):
        """Update completion statistics"""
        with self.lock:
            self.completed_files += 1
            self.total_trades += trades_count
            self.processing_times.append(processing_time)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        with self.lock:
            elapsed_time = time.time() - self.start_time
            
            # Overall speed
            overall_speed = self.completed_files / elapsed_time if elapsed_time > 0 else 0
            
            # Recent speed (based on last few files)
            if len(self.processing_times) > 5:
                avg_processing_time = sum(list(self.processing_times)[-10:]) / min(10, len(self.processing_times))
                recent_speed = 1 / avg_processing_time if avg_processing_time > 0 else 0
            else:
                recent_speed = overall_speed
            
            # ETA
            remaining_files = self.total_files - self.completed_files
            eta_seconds = remaining_files / overall_speed if overall_speed > 0 else 0
            eta_minutes = eta_seconds / 60
            
            # Memory status
            gpu_usage, _, _, _ = MemoryMonitor.get_gpu_memory_usage()
            sys_usage = MemoryMonitor.get_system_memory_usage()
            
            return {
                'completed_files': self.completed_files,
                'total_files': self.total_files,
                'overall_speed': overall_speed,
                'recent_speed': recent_speed,
                'eta_minutes': eta_minutes,
                'total_trades': self.total_trades,
                'gpu_usage': gpu_usage,
                'sys_usage': sys_usage,
                'elapsed_time': elapsed_time
            }

class ContinuousFileProcessor:
    """Processes files continuously using worker pipeline"""
    
    def __init__(self, strategies: List[Dict[str, Any]], performance_tracker: PerformanceTracker):
        self.strategies = strategies
        self.performance_tracker = performance_tracker
        self.processed_count = 0
        self.executor = ThreadPoolExecutor(max_workers=os.cpu_count()) # Initialize once
    
    async def process_single_file(self, file_info: Tuple[str, str, str]) -> List[Dict[str, Any]]:
        """Process a single file with timing and error handling"""
        file_path, symbol, timeframe = file_info
        start_time = time.time()
        
        try:
            # Check memory before processing
            if MemoryMonitor.should_throttle():
                logger.warning(f"Memory threshold reached, waiting before processing {symbol}")
                await asyncio.sleep(0.5)
                self._cleanup_memory()
            
            # Load data in executor to prevent blocking
            loop = asyncio.get_event_loop()
            data_arrays = await loop.run_in_executor(
                self.executor, # Use the shared executor
                file_io.load_data_for_gpu, 
                file_path, 
                self.strategies
            )
            
            if data_arrays is None:
                return []
            
            # Convert to DataFrame
            df = pl.DataFrame(data_arrays)
            
            # Generate signals for all strategies
            all_strategy_entries: Dict[str, np.ndarray] = {}
            all_strategy_exits: Dict[str, np.ndarray] = {}
            
            for strategy in self.strategies:
                strategy_name = strategy.get('name', 'UnknownStrategy')
                
                signals = signal_agent.get_signals_for_strategy(df, strategy_name, apply_intraday=True)
                
                if signals:
                    entry_long_arr = signals.get('entry_long', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)
                    entry_short_arr = signals.get('entry_short', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)
                    exit_long_arr = signals.get('exit_long', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)
                    exit_short_arr = signals.get('exit_short', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)
                    
                    combined_entries = np.logical_or(entry_long_arr, entry_short_arr)
                    combined_exits = np.logical_or(exit_long_arr, exit_short_arr)
                    
                    # Fix conflicts
                    conflict_mask = np.logical_and(combined_entries, combined_exits)
                    combined_exits = np.logical_and(combined_exits, ~conflict_mask)
                    
                    all_strategy_entries[strategy_name] = combined_entries
                    all_strategy_exits[strategy_name] = combined_exits
                else:
                    all_strategy_entries[strategy_name] = np.zeros(df.height, dtype=bool)
                    all_strategy_exits[strategy_name] = np.zeros(df.height, dtype=bool)
            
            # GPU processing (lock removed for concurrency)
            close_prices = data_arrays['close']
            results = real_gpu_accelerator.vectorized_backtest_gpu(
                close_prices, all_strategy_entries, all_strategy_exits, self.strategies
            )
            
            # Add metadata and count trades
            total_trades = 0
            for r in results:
                r['symbol'] = symbol
                r['timeframe'] = timeframe
                total_trades += r.get('total_trades', 0)
            
            # Update performance tracking
            processing_time = time.time() - start_time
            self.performance_tracker.update_completion(processing_time, total_trades)
            
            if total_trades > 0:
                logger.debug(f"✅ {symbol} ({timeframe}): {total_trades:,} trades in {processing_time:.2f}s")
            
            self.processed_count += 1
            
            # Periodic cleanup and status update
            if self.processed_count % MEMORY_CHECK_INTERVAL == 0:
                self._cleanup_memory()
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
            self._cleanup_memory()
            # Still update tracker even on error
            processing_time = time.time() - start_time
            self.performance_tracker.update_completion(processing_time, 0)
            return []
        finally:
            # Cleanup variables
            locals().clear()
    
    def _cleanup_memory(self):
        """Force memory cleanup"""
        gc.collect()
        real_gpu_accelerator.cleanup_gpu_memory()

async def worker_pipeline(worker_id: int, file_queue: asyncio.Queue, 
                         results_queue: asyncio.Queue, file_processor: ContinuousFileProcessor,
                         performance_tracker: PerformanceTracker):
    """Individual worker that continuously processes files"""
    
    logger.info(f"🚀 Worker {worker_id} started")
    
    while True:
        try:
            # Get next file from queue (with timeout to allow graceful shutdown)
            file_info = await asyncio.wait_for(file_queue.get(), timeout=1.0)
            
            if file_info is None:  # Sentinel value to stop worker
                logger.info(f"🛑 Worker {worker_id} stopping")
                break
            
            # Process the file
            results = await file_processor.process_single_file(file_info)
            
            # Put results in results queue
            await results_queue.put(results)
            
            # Mark task as done
            file_queue.task_done()
            
        except asyncio.TimeoutError:
            # No more files available, continue waiting
            continue
        except Exception as e:
            logger.error(f"Worker {worker_id} error: {e}")
            continue

async def stats_reporter(performance_tracker: PerformanceTracker, stop_event: asyncio.Event):
    """Continuously report performance statistics"""
    
    while not stop_event.is_set():
        try:
            await asyncio.sleep(STATS_UPDATE_INTERVAL)  # Update every N seconds
            
            stats = performance_tracker.get_stats()
            
            logger.info(
                f"📊 Progress: {stats['completed_files']}/{stats['total_files']} files | "
                f"Speed: {stats['overall_speed']:.2f}/s (recent: {stats['recent_speed']:.2f}/s) | "
                f"Trades: {stats['total_trades']:,} | "
                f"ETA: {stats['eta_minutes']:.1f}min | "
                f"GPU: {stats['gpu_usage']:.1%} | RAM: {stats['sys_usage']:.1%} | "
                f"Time: {stats['elapsed_time']:.0f}s"
            )
            
        except Exception as e:
            logger.error(f"Stats reporter error: {e}")

async def process_files_continuous_pipeline(feature_files: List[Tuple[str, str, str]], 
                                          strategies: List[Dict[str, Any]], 
                                          max_workers: int) -> List[Dict[str, Any]]:
    """Process files using continuous worker pipeline"""
    
    total_files = len(feature_files)
    performance_tracker = PerformanceTracker(total_files)
    file_processor = ContinuousFileProcessor(strategies, performance_tracker)
    
    # Create queues
    file_queue = asyncio.Queue(maxsize=max_workers * 2)  # Small buffer
    results_queue = asyncio.Queue()
    
    # Start stats reporter
    stop_event = asyncio.Event()
    stats_task = asyncio.create_task(stats_reporter(performance_tracker, stop_event))
    
    # Start workers
    workers = []
    for i in range(max_workers):
        worker = asyncio.create_task(
            worker_pipeline(i + 1, file_queue, results_queue, file_processor, performance_tracker)
        )
        workers.append(worker)
    
    # Feed files to queue
    async def feed_files():
        for file_info in feature_files:
            await file_queue.put(file_info)
        
        # Send stop signals to workers
        for _ in range(max_workers):
            await file_queue.put(None)
    
    # Start feeding files
    feeder_task = asyncio.create_task(feed_files())
    
    # Collect results
    all_results = []
    collected_results = 0
    
    while collected_results < total_files:
        try:
            results = await asyncio.wait_for(results_queue.get(), timeout=30.0)
            all_results.extend(results)
            collected_results += 1
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for results, checking if workers are done...")
            # Check if all workers are done
            if all(worker.done() for worker in workers):
                break
    
    # Clean shutdown
    stop_event.set()
    await feeder_task
    await asyncio.gather(*workers, return_exceptions=True)
    stats_task.cancel()
    file_processor.executor.shutdown(wait=True) # Shut down the executor
    
    return all_results

async def main_async():
    """Enhanced main function with continuous pipeline processing"""
    overall_start_time = time.time()
    
    logger.info("[INIT] Starting Continuous Pipeline Backtesting System")
    
    # Load strategies
    strategies = file_io.load_strategies()
    if not strategies:
        logger.error("[ERROR] No strategies loaded, exiting")
        return
    
    # Get feature files
    feature_files = file_io.get_available_feature_files()
    if not feature_files:
        logger.error("[ERROR] No feature files found, exiting")
        return
    
    logger.info(f"[INFO] Processing {len(feature_files)} files with {len(strategies)} strategies")
    logger.info(f"[INFO] Max concurrent workers: {MAX_CONCURRENT_WORKERS}")
    
    # System info
    gpu_usage, allocated, cached, total = MemoryMonitor.get_gpu_memory_usage()
    sys_usage = MemoryMonitor.get_system_memory_usage()
    logger.info(f"[SYSTEM] GPU Memory: {gpu_usage:.1%} used, System Memory: {sys_usage:.1%} used")
    
    Path(config.TEMP_DIR).mkdir(parents=True, exist_ok=True)
    
    if real_gpu_accelerator.cuda_available:
        logger.info("[INFO] Using GPU with continuous pipeline processing")
        
        all_backtest_results = await process_files_continuous_pipeline(
            feature_files, strategies, MAX_CONCURRENT_WORKERS
        )
        
    else:
        logger.warning("[WARNING] CUDA not available, falling back to sequential processing")
        all_backtest_results = []
    
    # Final statistics
    total_time = time.time() - overall_start_time
    total_trades = sum(r.get('total_trades', 0) for r in all_backtest_results)
    
    logger.info("[SUCCESS] CONTINUOUS PIPELINE BACKTESTING COMPLETED!")
    logger.info(f"[TIME] Total time: {total_time:.1f} seconds")
    logger.info(f"[THROUGHPUT] Files/second: {len(feature_files)/total_time:.2f}")
    logger.info(f"[FILES] Files processed: {len(feature_files)}")
    logger.info(f"[RESULTS] Total backtest results: {len(all_backtest_results)}")
    logger.info(f"[TRADES] Total trades generated: {total_trades:,}")
    
    # Final memory cleanup
    real_gpu_accelerator.cleanup_gpu_memory()
    gc.collect()

async def main():
    await main_async()

if __name__ == "__main__":
    # Optimize asyncio for high concurrency
    if sys.platform != 'win32':
        try:
            import uvloop
            asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        except ImportError:
            pass
    
    asyncio.run(main())