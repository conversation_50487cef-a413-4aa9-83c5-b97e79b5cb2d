import os
import asyncio
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
import re
import polars as pl
import numpy as np

from . import simulation
from . import performance
from .config import (
    DATA_DIR,
    OUTPUT_DIR,
    TEMP_DIR,
    TIMEFRAMES,
    COMPRESSION,
    COMPRESSION_LEVEL,
    STRATEGIES_FILE
)
from agents.signal_generation.signal_agent import SignalAgent

logger = logging.getLogger(__name__)

def get_available_feature_files() -> List[Tuple[str, str, str]]:
    """Get available feature files"""
    feature_files = []
    for file_path in Path(DATA_DIR).glob("*.parquet"):
        filename = file_path.name
        symbol, timeframe = extract_symbol_and_timeframe_from_filename(filename)
        if symbol and timeframe and timeframe in TIMEFRAMES:
            feature_files.append((str(file_path), symbol, timeframe))
    logger.info(f"[FOUND] {len(feature_files)} feature files")
    return feature_files

def extract_symbol_and_timeframe_from_filename(filename: str) -> <PERSON><PERSON>[Optional[str], Optional[str]]:
    """Extract symbol and timeframe from filename"""
    try:
        stem = Path(filename).stem
        if stem.startswith("features_"):
            stem = stem[9:]
        parts = stem.split('_')
        timeframe = parts[-1] if parts[-1] in TIMEFRAMES else None
        symbol = '_'.join(parts[:-1]) if timeframe else None
        return symbol, timeframe
    except Exception as e:
        pass
    return None, None

def generate_output_filename(symbol: str, timeframe: str) -> str:
    """Generate output filename"""
    return f"backtest_{symbol}_{timeframe}.parquet"

def generate_temp_filename(symbol: str, timeframe: str, strategy_name: str, rr_combo: List[float]) -> str:
    """Generate a unique temporary filename for each strategy, timeframe, stock, and RR combination."""
    rr_str = f"{rr_combo[0]}_{rr_combo[1]}".replace(".", "_")
    return f"temp_backtest_{symbol}_{timeframe}_{strategy_name}_{rr_str}.parquet"

async def write_symbol_results_async(results: List[Dict[str, Any]], symbol: str, timeframe: str):
    """Write results to file immediately after processing each feature file"""
    if not results:
        return
    
    try:
        output_filename = generate_output_filename(symbol, timeframe)
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        
        Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
        
        start_time = time.time()
        
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(
            None, 
            lambda: pl.DataFrame(results).write_parquet(output_path, compression=COMPRESSION, compression_level=COMPRESSION_LEVEL)
        )
        
        write_time = time.time() - start_time
        logger.info(f"[SUCCESS] Written {len(results)} results for {symbol} ({timeframe}) to {output_filename} in {write_time:.2f}s")
        
        if hasattr(os, 'sync'):
            os.sync()
            
    except Exception as e:
        logger.error(f"[ERROR] Error writing results for {symbol} ({timeframe}): {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

def round_to_tick(price: np.ndarray, tick_size: float = 0.05) -> np.ndarray:
    """Rounds a price array to the nearest tick size."""
    return np.round(price / tick_size) * tick_size

def load_data_for_gpu(file_path: str, strategies: List[Dict[str, Any]]) -> Optional[Dict[str, np.ndarray]]:
    """Load data from a Parquet file and format it for the GPU."""
    try:
        df = pl.read_parquet(file_path)
        
        if len(df) < 1000:
            logger.warning(f"Skipping {file_path} due to insufficient data ({len(df)} rows). Minimum 1000 rows required.")
            return None

        # Sort by datetime to ensure correct order for time-series operations
        if 'datetime' in df.columns:
            df = df.sort('datetime')
        else:
            logger.warning(f"No 'datetime' column to sort by for {file_path}. Data order not guaranteed.")

        # Dynamically get all required indicators from strategies
        required_indicators = set()
        for strategy in strategies:
            for signal_type in ['entry_long', 'entry_short', 'exit_long', 'exit_short']:
                expr_str = strategy.get(signal_type, "")
                if isinstance(expr_str, str):
                    potential_cols = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b(?!\()', expr_str)
                    required_indicators.update(potential_cols)

        # Base required columns
        required_cols = ['datetime', 'close', 'high', 'low', 'volume']
        
        # Add all unique indicators found in strategies
        for indicator in required_indicators:
            if indicator not in required_cols:
                required_cols.append(indicator)

        # Check for missing columns in the dataframe
        missing_cols = [col for col in required_cols if col not in df.columns and col not in ['Entry', 'stop_loss_pct', 'take_profit_pct']]
        if missing_cols:
            logger.error(f"Missing required columns for {file_path}: {missing_cols}")
            return None

        # Prepare data_arrays for GPU processing
        data_arrays = {}
        for col in df.columns:
            if col in required_cols:
                if df[col].dtype in [pl.Float32, pl.Float64, pl.Int32, pl.Int64]:
                    numpy_array = df[col].to_numpy().astype(np.float32)
                    if col in ['close', 'high', 'low', 'open']:
                        numpy_array = round_to_tick(numpy_array)
                    data_arrays[col] = numpy_array
                elif df[col].dtype == pl.Datetime:
                    data_arrays[col] = df[col].to_numpy()
        
        return data_arrays
    except Exception as e:
        logger.error(f"Failed to load data for GPU from {file_path}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

# The process_single_file_sync function is not used in the GPU path, so it remains unchanged.
# It's part of the CPU fallback or other workflows.
# No changes needed for process_single_file_sync based on this specific issue.

def load_strategies() -> List[Dict[str, Any]]:
    """Load strategies using SignalAgent"""
    try:
        signal_agent = SignalAgent(str(STRATEGIES_FILE))
        return signal_agent.get_strategies()
    except Exception as e:
        logger.error(f"Failed to initialize SignalAgent for loading strategies: {e}")
        return []
