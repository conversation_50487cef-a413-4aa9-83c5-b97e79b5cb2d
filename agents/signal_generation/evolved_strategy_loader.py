import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
import dataclasses

from agents.strategy_evolution.evolution_config import StrategyVariant
from agents.strategy_evolution.evolution_logger import logger

class EvolvedStrategyLoader:
    def __init__(self, evolution_strategies_path: str = "agents/config/evolution/"):
        self.evolution_strategies_path = Path(evolution_strategies_path)
        self.loaded_strategies_cache: Dict[str, StrategyVariant] = {}

    def load_evolved_strategy(self, stock_name: str, timeframe: str) -> Optional[StrategyVariant]:
        """Loads a single evolved strategy for a given stock and timeframe."""
        file_name = f"{stock_name}_{timeframe}.yaml"
        file_path = self.evolution_strategies_path / file_name

        if not file_path.exists():
            return None

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # Load all YAML documents from the file
                strategies_data = list(yaml.safe_load_all(f))
                if strategies_data:
                    # Assuming the last document is the most recent/relevant strategy
                    latest_strategy_dict = strategies_data[-1]
                    # Convert dictionary back to StrategyVariant dataclass
                    # Need to handle datetime objects correctly
                    if 'creation_date' in latest_strategy_dict and isinstance(latest_strategy_dict['creation_date'], str):
                        latest_strategy_dict['creation_date'] = datetime.fromisoformat(latest_strategy_dict['creation_date'])
                    if 'last_updated' in latest_strategy_dict and isinstance(latest_strategy_dict['last_updated'], str):
                        latest_strategy_dict['last_updated'] = datetime.fromisoformat(latest_strategy_dict['last_updated'])
                    
                    # Handle Enum conversion
                    if 'status' in latest_strategy_dict and isinstance(latest_strategy_dict['status'], str):
                        latest_strategy_dict['status'] = StrategyStatus(latest_strategy_dict['status'])
                    if 'market_regime' in latest_strategy_dict and isinstance(latest_strategy_dict['market_regime'], str):
                        latest_strategy_dict['market_regime'] = MarketRegime(latest_strategy_dict['market_regime'])

                    strategy_variant = StrategyVariant(**latest_strategy_dict)
                    self.loaded_strategies_cache[f"{stock_name}_{timeframe}"] = strategy_variant
                    return strategy_variant
            return None
        except Exception as e:
            logger.error(f"Error loading evolved strategy from {file_path}: {e}")
            return None

    def load_all_evolved_strategies(self) -> List[StrategyVariant]:
        """Loads all evolved strategies from the evolution folder."""
        all_evolved_strategies = []
        if not self.evolution_strategies_path.exists():
            return []

        for file_path in self.evolution_strategies_path.glob("*.yaml"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    strategies_data = list(yaml.safe_load_all(f))
                    if strategies_data:
                        for strategy_dict in strategies_data:
                            if 'creation_date' in strategy_dict and isinstance(strategy_dict['creation_date'], str):
                                strategy_dict['creation_date'] = datetime.fromisoformat(strategy_dict['creation_date'])
                            if 'last_updated' in strategy_dict and isinstance(strategy_dict['last_updated'], str):
                                strategy_dict['last_updated'] = datetime.fromisoformat(strategy_dict['last_updated'])
                            
                            if 'status' in strategy_dict and isinstance(strategy_dict['status'], str):
                                strategy_dict['status'] = StrategyStatus(strategy_dict['status'])
                            if 'market_regime' in strategy_dict and isinstance(strategy_dict['market_regime'], str):
                                strategy_dict['market_regime'] = MarketRegime(strategy_dict['market_regime'])

                            strategy_variant = StrategyVariant(**strategy_dict)
                            all_evolved_strategies.append(strategy_variant)
            except Exception as e:
                logger.error(f"Error loading evolved strategy from {file_path}: {e}")
        return all_evolved_strategies
