"""
Default fitness metrics for consistent evaluation across the strategy evolution system.
"""

DEFAULT_FITNESS_METRICS = {
    "composite_score": 0.01,  # Small positive score to give new strategies a chance
    "sharpe_ratio": 0.01,
    "max_drawdown": 100.0,    # High value, less penalizing for new strategies
    "roi": 0.001,
    "profitability": 0.001,
    "win_rate": 0.01,
    "volatility": 0.01,
    "num_trades": 1,          # At least one trade for a valid strategy
}

def get_default_fitness_metrics() -> dict:
    """
    Returns a consistent set of default fitness metrics.
    """
    return DEFAULT_FITNESS_METRICS.copy()
