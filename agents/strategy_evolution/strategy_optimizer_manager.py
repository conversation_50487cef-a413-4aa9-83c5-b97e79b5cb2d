import asyncio
import uuid
from typing import Dict, List, Any, Tu<PERSON>
from datetime import datetime

from agents.strategy_evolution.evolution_config import EvolutionConfig, StrategyVariant
from agents.strategy_evolution.evolution_logger import logger
from agents.strategy_evolution.strategy_variant_manager import StrategyVariantManager
from agents.strategy_evolution.gpu_processing_manager import GPUProcessingManager
from agents.strategy_evolution.strategy_fitness_evaluator import StrategyFitnessEvaluator
from utils.gpu_hyperopt_free import gpu_hyperopt_free

class StrategyOptimizerManager:
    def __init__(self, evolution_config: EvolutionConfig, variant_manager: StrategyVariantManager, 
                 gpu_manager: GPUProcessingManager, strategy_fitness_evaluator: StrategyFitnessEvaluator):
        self.evolution_config = evolution_config
        self.variant_manager = variant_manager
        self.gpu_manager = gpu_manager
        self.strategy_fitness_evaluator = strategy_fitness_evaluator
        self.evolution_stats = {'variants_generated': 0, 'variants_above_threshold': 0, 'optimization_tasks_completed': 0, 'optimization_tasks_failed': 0} # Initialize stats

    def _get_parameter_space(self, base_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Get parameter space using variant manager"""
        return self.variant_manager.get_parameter_space(base_strategy)

    async def run_multi_objective_optimization(self, base_strategy: Dict[str, Any],
                                             stock_name: str, timeframe: str) -> List[StrategyVariant]:
        """
        Run GPU-accelerated optimization using custom GPU hyperparameter optimizer
        """
        try:
            # Update progress tracking (assuming evolution_stats is managed externally or passed)
            # For now, we'll update a local copy and return it if needed.
            # self.evolution_stats['current_stock'] = stock_name
            # self.evolution_stats['current_strategy'] = base_strategy['name']

            # Show stock-specific progress
            logger.stock_progress(stock_name, f"GPU optimizing {base_strategy['name']} ({timeframe})")

            # Use free GPU hyperparameter optimizer (no Optuna dependency)
            optimization_result = gpu_hyperopt_free.optimize_strategy_parameters(
                objective_func=lambda params: self.strategy_fitness_evaluator.evaluate_strategy_fitness_sync_with_params(
                    base_strategy, stock_name, timeframe, params
                ),
                param_space=self._get_parameter_space(base_strategy),
                n_trials=50,
                method='auto'
            )

            self.evolution_stats['optimization_tasks_completed'] += 1

            # Create variants from optimization results
            optimized_variants = []
            # Check if optimization_result is valid and has a good score
            if optimization_result and optimization_result.best_score > self.evolution_config.min_optimization_score_threshold:
                best_params = optimization_result.best_params
                best_metrics = optimization_result.all_metrics # Get all metrics directly

                # Create variant with optimized parameters
                variant = StrategyVariant(
                    strategy_id=str(uuid.uuid4()),
                    base_strategy_name=base_strategy['name'],
                    stock_name=stock_name,
                    timeframe=timeframe,
                    ranking=0, # Will be set based on composite score
                    entry_conditions={
                        'oversold_threshold': best_params.get('oversold_threshold', 30),
                        'overbought_threshold': best_params.get('overbought_threshold', 70)
                    },
                    exit_conditions=base_strategy.get('exit_conditions', {}), # Use base strategy's exit
                    intraday_rules=base_strategy.get('intraday_rules', {}),
                    risk_reward_ratios=best_params.get('risk_reward_ratio', [[1, 2]]), # Use optimized or default
                    risk_management={
                        'stop_loss': best_params.get('stop_loss', 0.02),
                        'take_profit': best_params.get('take_profit', 0.04)
                    },
                    position_sizing={
                        'risk_per_trade': best_params.get('risk_per_trade', 0.02)
                    },
                    performance_metrics=best_metrics, # Assign all metrics directly
                    creation_date=datetime.now(),
                    last_updated=datetime.now()
                )
                
                # Calculate ranking based on the composite score from optimization_result
                composite_score = best_metrics.get('composite_score', 0.0)
                scaled_ranking = max(10, min(100, int(composite_score * 100))) # Scale to 10-100
                variant.ranking = scaled_ranking
                
                # Only keep variants above threshold
                if variant.ranking >= self.evolution_config.min_ranking_threshold:
                    optimized_variants.append(variant)

            # Log progress for this stock
            logger.stock_progress(stock_name, f"GPU generated {len(optimized_variants)} variants (score: {optimization_result.best_score:.3f})")

            # Update evolution stats
            self.evolution_stats['variants_generated'] += len(optimized_variants)
            self.evolution_stats['variants_above_threshold'] += len(optimized_variants)
            self.evolution_stats['optimization_tasks_completed'] += 1 # Increment here as optimization was attempted

            # Cleanup GPU memory (assuming gpu_hyperopt_free has this method)
            if hasattr(gpu_hyperopt_free, 'cleanup_gpu_memory'):
                gpu_hyperopt_free.cleanup_gpu_memory()

            return optimized_variants

        except Exception as e:
            logger.error(f"Error in GPU multi-objective optimization: {e}")
            self.evolution_stats['optimization_tasks_failed'] += 1
            return []
