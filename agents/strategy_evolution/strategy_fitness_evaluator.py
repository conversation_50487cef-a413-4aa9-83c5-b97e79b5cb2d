import asyncio
import uuid
from typing import Dict, List, Any, <PERSON><PERSON>
from datetime import datetime
from pathlib import Path
import polars as pl
import torch

from agents.backtesting.enhanced_backtesting_kimi import run_backtesting_for_evolution
from agents.strategy_evolution.evolution_config import EvolutionConfig, StrategyVariant
from agents.strategy_evolution.evolution_logger import logger
from agents.strategy_evolution.strategy_evaluator import get_cuda_optimizer, process_strategies_parallel_async
from agents.strategy_evolution.strategy_variant_manager import StrategyVariantManager
from agents.strategy_evolution.gpu_processing_manager import GPUProcessingManager
from agents.strategy_evolution.default_metrics import get_default_fitness_metrics as get_canonical_default_metrics

class StrategyFitnessEvaluator:
    def __init__(self, evolution_config: EvolutionConfig, variant_manager: StrategyVariantManager, gpu_manager: GPUProcessingManager):
        self.evolution_config = evolution_config
        self.variant_manager = variant_manager
        self.gpu_manager = gpu_manager

    def _get_default_fitness_metrics(self) -> Dict[str, float]:
        """Get default fitness metrics from the canonical source"""
        return get_canonical_default_metrics()

    def _calculate_composite_fitness(self, metrics: Dict[str, float]) -> float:
        """Calculate composite fitness using GPU manager"""
        return self.gpu_manager.calculate_composite_fitness(metrics)

    async def evaluate_strategy_fitness(self, strategy_variant: StrategyVariant) -> Dict[str, float]:
        """
        Evaluate strategy fitness using the backtesting agent

        This addresses Enhancement Point #1: Full Backtesting Integration
        """
        try:
            logger.debug(f"[EVAL] Evaluating fitness for {strategy_variant.strategy_id} on {strategy_variant.stock_name}")

            # Convert strategy variant to backtesting format
            strategy_config = self.variant_manager.variant_to_backtesting_format(strategy_variant)

            # Run backtesting using the existing backtesting agent
            backtesting_results = await run_backtesting_for_evolution(
                strategies=[strategy_config],
                max_symbols=1,  # Test on specific stock
                max_files=self.evolution_config.backtesting_config["max_files"],
                ranking_threshold=0  # Allow all rankings for evaluation
            )

            if not backtesting_results.get('success', False):
                error_message = f"Backtesting failed for {strategy_variant.strategy_id}. Details: {backtesting_results.get('error', 'No error details provided.')}"
                logger.error(error_message)
                raise RuntimeError(error_message)

            # Extract performance metrics
            strategy_performance = backtesting_results.get('strategy_performance', {})
            strategy_name = strategy_variant.base_strategy_name

            if strategy_name not in strategy_performance:
                logger.warning(f"No performance data for {strategy_name}")
                return self._get_default_fitness_metrics()

            perf_data = strategy_performance[strategy_name]

            # Calculate fitness metrics
            fitness_metrics = {
                'sharpe_ratio': perf_data.get('avg_sharpe', 0.0),
                'max_drawdown': abs(perf_data.get('max_drawdown', 100.0)),  # Make positive for minimization
                'win_rate': perf_data.get('avg_accuracy', 0.0),
                'total_trades': perf_data.get('total_trades', 0),
                'total_pnl': perf_data.get('total_pnl', 0.0),
                'roi': perf_data.get('total_roi', 0.0)
            }

            # Update strategy variant performance metrics
            strategy_variant.performance_metrics = fitness_metrics
            strategy_variant.last_updated = datetime.now()

            # Calculate composite fitness score
            composite_score = self._calculate_composite_fitness(fitness_metrics)
            fitness_metrics['composite_score'] = composite_score

            logger.debug(f"[EVAL] Fitness for {strategy_variant.strategy_id} completed. Score: {composite_score:.2f}")
            return fitness_metrics

        except Exception as e:
            logger.error(f"Error evaluating strategy fitness: {e}")
            return self._get_default_fitness_metrics()

    async def evaluate_strategy_fitness_batch(self, strategy_variants: List[StrategyVariant]) -> Dict[str, Dict[str, float]]:
        """
        Evaluate multiple strategy variants in batch for better performance

        This leverages the backtesting agent's parallel processing capabilities
        """
        try:
            if not strategy_variants:
                return {}

            logger.info(f"[EVAL] Starting batch fitness evaluation for {len(strategy_variants)} variants.")

            # Convert all variants to backtesting format
            strategy_configs = []
            variant_mapping = {}  # Map strategy names to variants

            for variant in strategy_variants:
                strategy_config = self.variant_manager.variant_to_backtesting_format(variant)
                strategy_configs.append(strategy_config)
                variant_mapping[variant.base_strategy_name] = variant

            # Run batch backtesting
            backtesting_results = await run_backtesting_for_evolution(
                strategies=strategy_configs,
                max_symbols=self.evolution_config.backtesting_config.get("max_symbols", 10),
                max_files=self.evolution_config.backtesting_config["max_files"],
                ranking_threshold=0  # Allow all rankings for evaluation
            )

            if not backtesting_results.get('success', False):
                logger.error(f"Batch backtesting failed: {backtesting_results.get('error', 'Unknown error')}")
                return {variant.strategy_id: self._get_default_fitness_metrics() for variant in strategy_variants}

            # Process results for each variant
            results = {}
            strategy_performance = backtesting_results.get('strategy_performance', {})

            for variant in strategy_variants:
                strategy_name = variant.base_strategy_name

                if strategy_name in strategy_performance:
                    perf_data = strategy_performance[strategy_name]

                    # Calculate fitness metrics
                    fitness_metrics = {
                        'sharpe_ratio': perf_data.get('avg_sharpe', 0.0),
                        'max_drawdown': abs(perf_data.get('max_drawdown', 100.0)),
                        'win_rate': perf_data.get('avg_accuracy', 0.0),
                        'total_trades': perf_data.get('total_trades', 0),
                        'total_pnl': perf_data.get('total_pnl', 0.0),
                        'roi': perf_data.get('total_roi', 0.0)
                    }

                    # Calculate composite fitness score
                    composite_score = self._calculate_composite_fitness(fitness_metrics)
                    fitness_metrics['composite_score'] = composite_score

                    # Update variant
                    variant.performance_metrics = fitness_metrics
                    variant.last_updated = datetime.now()

                    results[variant.strategy_id] = fitness_metrics
                    logger.debug(f"[EVAL] Batch fitness for {strategy_name} completed. Score: {composite_score:.2f}")
                else:
                    logger.warning(f"No performance data for {strategy_name}, using default metrics.")
                    results[variant.strategy_id] = self._get_default_fitness_metrics()

            logger.info(f"🎯 Batch evaluation completed for {len(results)} variants")
            return results

        except Exception as e:
            logger.error(f"Error in batch strategy fitness evaluation: {e}")
            return {variant.strategy_id: self._get_default_fitness_metrics() for variant in strategy_variants}

    async def evaluate_strategy_fitness_gpu(self, strategy_variant: StrategyVariant) -> Dict[str, float]:
        """
        GPU-accelerated strategy fitness evaluation using existing backtesting agent's GPU processing
        """
        try:
            # Get CUDA optimizer from backtesting agent
            cuda_optimizer = get_cuda_optimizer()
            if not cuda_optimizer or not cuda_optimizer.cuda_available:
                logger.warning("CUDA not available, falling back to CPU evaluation")
                return await self._evaluate_strategy_fitness_async(strategy_variant)

            # Load feature data for the specific stock
            stock_files = list(Path("data/features").glob(f"features_{strategy_variant.stock_name}_*.parquet"))
            if not stock_files:
                logger.warning(f"No feature data found for {strategy_variant.stock_name}")
                return self._get_default_fitness_metrics()

            # Use the first available timeframe file
            feature_file = stock_files[0]
            df = pl.read_parquet(feature_file)

            # Convert strategy variant to backtesting format
            strategy_config = self.variant_manager.variant_to_backtesting_format(strategy_variant)

            # Use backtesting agent's GPU parallel processing
            # Expecting process_strategies_parallel_async to return a dictionary of metrics directly
            parallel_results_metrics = await process_strategies_parallel_async(df, [strategy_config], cuda_optimizer)
            
            if parallel_results_metrics and strategy_variant.base_strategy_name in parallel_results_metrics:
                fitness_metrics = parallel_results_metrics[strategy_variant.base_strategy_name]
                
                # Ensure all required metrics are present, provide defaults if missing
                fitness_metrics.setdefault('sharpe_ratio', 0.0)
                fitness_metrics.setdefault('max_drawdown', 100.0)
                fitness_metrics.setdefault('win_rate', 0.0)
                fitness_metrics.setdefault('total_trades', 0)
                fitness_metrics.setdefault('total_pnl', 0.0)
                fitness_metrics.setdefault('roi', 0.0)

                # Calculate composite fitness score if not already present
                if 'composite_score' not in fitness_metrics:
                    composite_score = self._calculate_composite_fitness(fitness_metrics)
                    fitness_metrics['composite_score'] = composite_score
                else:
                    composite_score = fitness_metrics['composite_score']
                
                # Update strategy variant
                strategy_variant.performance_metrics = fitness_metrics
                strategy_variant.last_updated = datetime.now()
                
                logger.debug(f"GPU evaluation for {strategy_variant.stock_name}: Sharpe={fitness_metrics.get('sharpe_ratio', 0):.2f}")
                return fitness_metrics
            else:
                error_message = f"GPU backtesting failed or returned no comprehensive metrics for {strategy_variant.stock_name}. Expected full metrics, got: {parallel_results_metrics}"
                logger.error(error_message)
                raise RuntimeError(error_message) # Raise error as per requirement

        except Exception as e:
            logger.error(f"GPU fitness evaluation failed for {strategy_variant.stock_name}: {e}")
            raise # Re-raise the exception to ensure failures are not silently ignored

    async def _evaluate_strategy_fitness_async(self, strategy_variant: StrategyVariant) -> Dict[str, float]:
        """
        GPU-accelerated REAL DATA evaluation - uses actual market data for fitness evaluation
        This is critical for meaningful evolution!
        """
        try:
            # Load real market data for the specific stock
            stock_files = list(Path("data/features").glob(f"features_{strategy_variant.stock_name}_*.parquet"))
            if not stock_files:
                logger.warning(f"No real data found for {strategy_variant.stock_name}, using default metrics")
                return self._get_default_fitness_metrics()

            # Use the timeframe-specific file if available
            target_file = None
            for file_path in stock_files:
                if strategy_variant.timeframe in str(file_path):
                    target_file = file_path
                    break

            if not target_file:
                target_file = stock_files[0]  # Use first available file

            # Convert strategy variant to backtesting format
            strategy_config = self.variant_manager.variant_to_backtesting_format(strategy_variant)

            # Use GPU-accelerated backtesting for real evaluation
            try:
                # Run GPU-accelerated backtesting using the enhanced backtesting system
                backtest_results = await run_backtesting_for_evolution(
                    strategies=[strategy_config],
                    max_symbols=1,  # Single stock evaluation
                    max_files=1,    # Single file evaluation
                    ranking_threshold=0
                )

                if backtest_results and 'strategy_performance' in backtest_results:
                    strategy_name = strategy_config.get('name', 'Unknown')
                    if strategy_name in backtest_results['strategy_performance']:
                        perf_data = backtest_results['strategy_performance'][strategy_name]

                        # Convert to fitness metrics format
                        real_metrics = {
                            'sharpe_ratio': perf_data.get('avg_sharpe', 0.0),
                            'max_drawdown': perf_data.get('max_drawdown', 100.0),
                            'win_rate': perf_data.get('avg_accuracy', 0.0),
                            'total_trades': perf_data.get('total_trades', 0),
                            'total_pnl': perf_data.get('total_pnl', 0.0),
                            'roi': perf_data.get('total_roi', 0.0)
                        }

                        # Calculate composite fitness score
                        composite_score = self._calculate_composite_fitness(real_metrics)
                        real_metrics['composite_score'] = composite_score

                        logger.debug(f"GPU evaluation for {strategy_variant.stock_name}: Sharpe={real_metrics.get('sharpe_ratio', 0):.2f}")
                        return real_metrics
                    else:
                        logger.warning(f"No performance data found for strategy {strategy_name}")
                        return self._get_default_fitness_metrics()
                else:
                    logger.warning(f"GPU backtesting returned no results for {strategy_variant.stock_name}")
                    return self._get_default_fitness_metrics()

            except Exception as gpu_error:
                error_message = f"GPU backtesting failed for {strategy_variant.stock_name}: {gpu_error}. No fallback to simulation allowed."
                logger.error(error_message)
                raise RuntimeError(error_message) # Raise error as per requirement

        except Exception as e:
            logger.error(f"Error in GPU fitness evaluation for {strategy_variant.stock_name}: {e}")
            raise # Re-raise the exception to ensure failures are not silently ignored

    def _evaluate_strategy_fitness_sync(self, strategy_variant: StrategyVariant) -> Dict[str, float]:
        """
        Synchronous wrapper for async GPU evaluation - for compatibility with Optuna
        """
        try:
            # Run async evaluation in event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, create a new task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._evaluate_strategy_fitness_async(strategy_variant))
                    return future.result(timeout=30)  # 30 second timeout
            else:
                # If no event loop is running, run directly
                return asyncio.run(self._evaluate_strategy_fitness_async(strategy_variant))
        except Exception as e:
            logger.error(f"Error in sync fitness evaluation wrapper: {e}")
            return self._get_default_fitness_metrics()

    def evaluate_strategy_fitness_sync_with_params(self, base_strategy: Dict[str, Any],
                                                  stock_name: str, timeframe: str,
                                                  params: Dict[str, Any]) -> Dict[str, float]:
        """
        Evaluate strategy fitness with specific parameters for free GPU optimization.
        Returns a dictionary of all performance metrics.
        """
        try:
            # Create strategy variant with optimized parameters
            strategy_variant = StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_strategy['name'],
                stock_name=stock_name,
                timeframe=timeframe,
                parameters=params,
                risk_reward_ratio=params.get('risk_reward_ratio', [[1, 2]]), # Default to a list of lists
                performance_metrics={}
            )

            # Evaluate fitness
            fitness_metrics = self._evaluate_strategy_fitness_sync(strategy_variant)
            
            # Ensure composite_score is present, even if 0.0
            if 'composite_score' not in fitness_metrics:
                fitness_metrics['composite_score'] = self._calculate_composite_fitness(fitness_metrics)

            return fitness_metrics

        except Exception as e:
            logger.error(f"Parameter evaluation failed for {base_strategy['name']} on {stock_name}-{timeframe} with params {params}: {e}")
            # Return default metrics on failure, but log as error
            return self._get_default_fitness_metrics()
