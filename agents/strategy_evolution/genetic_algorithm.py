#!/usr/bin/env python3
"""
Genetic Algorithm Module for Strategy Evolution

This module handles all genetic algorithm operations including:
- DNA extraction and conversion
- Mutation, crossover, and selection operations
- Population management
"""

import copy
import random
import re
import uuid
from datetime import datetime
from typing import List, Tuple, Dict, Any

import numpy as np

from .data_types import (
    StrategyChromosome, StrategyRule, MutationType, TimeWindow,
    EvolutionConfig, EvolutionState
)
from .evolution_logger import logger
from .lazy_imports import log_critical


class GeneticAlgorithm:
    """
    Handles all genetic algorithm operations for strategy evolution.
    """
    
    def __init__(self, evolution_config: EvolutionConfig, generation_counter: int = 0):
        self.evolution_config = evolution_config
        self.generation_counter = generation_counter
        
        # Genetic Algorithm parameters from config
        ga_params = evolution_config.genetic_algorithm_params
        self.population_size = ga_params.get('population_size', 50)
        self.mutation_rate = ga_params.get('mutation_rate', 0.15)
        self.crossover_rate = ga_params.get('crossover_rate', 0.8)
        self.elite_size = ga_params.get('elite_size', 5)
        self.tournament_size = ga_params.get('tournament_size', 3)
        
        logger.info("[GENETIC] Genetic Algorithm initialized with dynamic configuration")

    def set_generation_counter(self, generation: int):
        """Update the generation counter"""
        self.generation_counter = generation

    def create_strategy_dna(self, variant: StrategyChromosome) -> Dict[str, float]:
        """Extract DNA (numerical parameters) from strategy variant for genetic operations"""
        try:
            dna = {}
            for gene_name, gene in variant.genes.items():
                if gene.gene_type in ['numeric', 'boolean']:
                    dna[gene_name] = float(gene.value)
            return dna
        except Exception as e:
            logger.error(f"Error creating strategy DNA: {e}")
            return {}

    def dna_to_variant(self, base_variant: StrategyChromosome, dna: Dict[str, float]) -> StrategyChromosome:
        """Convert DNA back to a new strategy chromosome"""
        try:
            new_variant = copy.deepcopy(base_variant)
            new_variant.strategy_id = str(uuid.uuid4())
            new_variant.generation = self.generation_counter
            new_variant.parent_ids = [base_variant.strategy_id]
            new_variant.creation_timestamp = datetime.now()

            for gene_name, gene_value in dna.items():
                if gene_name in new_variant.genes:
                    gene = new_variant.genes[gene_name]
                    
                    # Apply bounds and type conversion
                    if gene.gene_type == 'numeric':
                        if gene.min_value is not None and gene.max_value is not None:
                            new_value = max(gene.min_value, min(gene.max_value, gene_value))
                            if isinstance(gene.value, int):
                                new_value = int(round(new_value))
                            gene.value = new_value
                        else:
                            gene.value = gene_value
                    elif gene.gene_type == 'boolean':
                        gene.value = bool(gene_value > 0.5)
            
            return new_variant
        except Exception as e:
            logger.error(f"Error converting DNA to variant: {e}")
            return None

    def mutate_chromosome(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Apply mutation to a chromosome"""
        mutated_chromosome = copy.deepcopy(chromosome)
        mutated_chromosome.strategy_id = str(uuid.uuid4())
        mutated_chromosome.strategy_name = f"{chromosome.strategy_name}_M{self.generation_counter}"
        mutated_chromosome.generation = self.generation_counter
        mutated_chromosome.parent_ids = [chromosome.strategy_id]
        mutated_chromosome.creation_timestamp = datetime.now()

        mutation_applied = False

        # 1. Parameter tweaking (DNA mutation)
        if random.random() < self.mutation_rate:
            mutation_applied = True
            dna = self.create_strategy_dna(mutated_chromosome)
            mutated_dna = self._mutate_dna(dna)
            mutated_chromosome = self.dna_to_variant(mutated_chromosome, mutated_dna)

        # 2. Rule-based mutations
        if random.random() < self.evolution_config.rule_mutation_rate:
            mutation_applied = True
            self._apply_rule_mutations(mutated_chromosome)

        # 3. Condition simplification
        if random.random() < self.evolution_config.condition_simplification_rate:
            mutation_applied = True
            self._apply_condition_simplification(mutated_chromosome)

        # 4. New feature injection
        if random.random() < 0.1:  # 10% chance
            mutation_applied = True
            self._apply_feature_injection(mutated_chromosome)

        # Ensure at least some mutation occurred
        if not mutation_applied:
            dna = self.create_strategy_dna(mutated_chromosome)
            mutated_dna = self._mutate_dna(dna, mutation_rate_multiplier=0.5)
            mutated_chromosome = self.dna_to_variant(mutated_chromosome, mutated_dna)

        return mutated_chromosome

    def _mutate_dna(self, dna: Dict[str, float], mutation_rate_multiplier: float = 1.0) -> Dict[str, float]:
        """Apply mutation to DNA with parameter-specific strength"""
        mutated_dna = dna.copy()
        mutation_rate = self.mutation_rate * mutation_rate_multiplier

        for key, value in mutated_dna.items():
            if random.random() < mutation_rate:
                # Define mutation strength based on parameter type
                if 'threshold' in key:
                    mutation_strength = 5.0
                elif 'stop_loss' in key or 'take_profit' in key or 'risk_per_trade' in key:
                    mutation_strength = 0.005
                elif 'period' in key or 'max_trades' in key:
                    mutation_strength = 2.0
                else:
                    mutation_strength = 0.1
                
                mutation = random.gauss(0, mutation_strength)
                mutated_dna[key] = value + mutation
        
        return mutated_dna

    def _apply_rule_mutations(self, chromosome: StrategyChromosome):
        """Apply rule-based mutations to strategy conditions"""
        if not chromosome.rules:
            return

        rule_to_mutate = random.choice(chromosome.rules)
        mutation_type = random.choice(self.evolution_config.mutation_types)

        if mutation_type == MutationType.CONDITION_MODIFY:
            self._mutate_rule_condition(rule_to_mutate)
        elif mutation_type == MutationType.INDICATOR_REPLACE:
            self._mutate_rule_indicator(rule_to_mutate)
        elif mutation_type == MutationType.LOGIC_SIMPLIFY:
            self._simplify_rule_logic(rule_to_mutate)
        elif mutation_type == MutationType.NEW_FEATURE_INJECT:
            self._inject_new_feature(rule_to_mutate)

    def _mutate_rule_condition(self, rule: StrategyRule):
        """Mutate a rule's condition by modifying operators or thresholds"""
        condition = rule.condition
        
        # Replace comparison operators
        operator_replacements = {'<': '<=', '<=': '<', '>': '>=', '>=': '>', '==': '!=', '!=': '=='}
        for old_op, new_op in operator_replacements.items():
            if old_op in condition and random.random() < 0.5:
                condition = condition.replace(old_op, new_op, 1)
                break

        # Modify numeric thresholds
        numbers = re.findall(r'\d+\.?\d*', condition)
        if numbers:
            old_number = random.choice(numbers)
            try:
                old_value = float(old_number)
                change_factor = random.uniform(0.8, 1.2)
                new_value = old_value * change_factor
                new_number = f"{new_value:.2f}" if '.' in old_number else str(int(new_value))
                condition = condition.replace(old_number, new_number, 1)
            except ValueError:
                pass
        
        rule.condition = condition

    def _mutate_rule_indicator(self, rule: StrategyRule):
        """Replace indicators in a rule with plausible alternatives"""
        indicator_replacements = {
            'RSI': ['STOCH_K', 'MFI', 'CCI'], 'EMA': ['SMA', 'WMA', 'DEMA'],
            'MACD': ['PPO', 'TRIX'], 'Volume': ['OBV']
        }
        condition = rule.condition
        for old_indicator, replacements in indicator_replacements.items():
            if old_indicator in condition:
                new_indicator = random.choice(replacements)
                condition = condition.replace(old_indicator, new_indicator)
                
                # Update rule indicators list
                for i, ind in enumerate(rule.indicators):
                    if old_indicator in ind:
                        rule.indicators[i] = ind.replace(old_indicator, new_indicator)
                break
        rule.condition = condition

    def _simplify_rule_logic(self, rule: StrategyRule):
        """Simplify rule logic by removing redundant parts"""
        condition = rule.condition
        condition = condition.replace('not not ', '')
        condition = re.sub(r'\s+and\s+and\s+', ' and ', condition)
        condition = re.sub(r'\s+or\s+or\s+', ' or ', condition)
        rule.condition = condition

    def _inject_new_feature(self, rule: StrategyRule):
        """Inject new technical indicators into a rule"""
        new_indicators = ['ADX', 'ATR', 'BB_width', 'Donchian_high']
        new_indicator = random.choice(new_indicators)
        operator = random.choice(['>', '<', '>=', '<='])
        threshold = random.uniform(10, 100)
        new_condition_part = f"{new_indicator} {operator} {threshold:.2f}"
        connector = random.choice([' and ', ' or '])
        rule.condition = f"({rule.condition}){connector}({new_condition_part})"
        if new_indicator not in rule.indicators:
            rule.indicators.append(new_indicator)

    def _apply_condition_simplification(self, chromosome: StrategyChromosome):
        """Apply condition simplification across all rules"""
        for rule in chromosome.rules:
            self._simplify_rule_logic(rule)

    def _apply_feature_injection(self, chromosome: StrategyChromosome):
        """Apply feature injection to a random rule"""
        if chromosome.rules:
            rule_to_modify = random.choice(chromosome.rules)
            self._inject_new_feature(rule_to_modify)

    def crossover_chromosomes(self, parent1: StrategyChromosome, parent2: StrategyChromosome) -> Tuple[StrategyChromosome, StrategyChromosome]:
        """Perform crossover between two chromosomes"""
        offspring1 = copy.deepcopy(parent1)
        offspring2 = copy.deepcopy(parent2)

        # Setup offspring metadata
        for offspring in [offspring1, offspring2]:
            offspring.strategy_id = str(uuid.uuid4())
            offspring.generation = self.generation_counter
            offspring.parent_ids = [parent1.strategy_id, parent2.strategy_id]
            offspring.creation_timestamp = datetime.now()
        offspring1.strategy_name = f"{parent1.strategy_name}_X_{self.generation_counter}"
        offspring2.strategy_name = f"{parent2.strategy_name}_X_{self.generation_counter}"

        # Crossover DNA (parameters)
        parent1_dna = self.create_strategy_dna(parent1)
        parent2_dna = self.create_strategy_dna(parent2)
        child1_dna, child2_dna = self._crossover_dna(parent1_dna, parent2_dna)
        offspring1 = self.dna_to_variant(offspring1, child1_dna)
        offspring2 = self.dna_to_variant(offspring2, child2_dna)

        # Crossover rules
        self._crossover_rules(offspring1, offspring2, parent1, parent2)

        return offspring1, offspring2

    def _crossover_dna(self, parent1_dna: Dict[str, float], parent2_dna: Dict[str, float]) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Perform uniform crossover on DNA"""
        child1_dna, child2_dna = {}, {}
        for key in parent1_dna.keys():
            if random.random() < self.crossover_rate:
                if random.random() < 0.5:
                    child1_dna[key] = parent1_dna[key]
                    child2_dna[key] = parent2_dna[key]
                else:
                    child1_dna[key] = parent2_dna[key]
                    child2_dna[key] = parent1_dna[key]
            else:
                child1_dna[key] = parent1_dna[key]
                child2_dna[key] = parent2_dna[key]
        return child1_dna, child2_dna

    def _crossover_rules(self, offspring1: StrategyChromosome, offspring2: StrategyChromosome, parent1: StrategyChromosome, parent2: StrategyChromosome):
        """Perform crossover for strategy rules"""
        all_rules = parent1.rules + parent2.rules
        if all_rules:
            random.shuffle(all_rules)
            mid_point = len(all_rules) // 2
            offspring1.rules = all_rules[:mid_point]
            offspring2.rules = all_rules[mid_point:]
            if not offspring1.rules: offspring1.rules = [random.choice(parent1.rules)] if parent1.rules else []
            if not offspring2.rules: offspring2.rules = [random.choice(parent2.rules)] if parent2.rules else []

    def tournament_selection(self, population: List[StrategyChromosome]) -> StrategyChromosome:
        """Select a chromosome using tournament selection based on fitness score"""
        if not population:
            return None
        tournament = random.sample(population, min(self.tournament_size, len(population)))
        return max(tournament, key=lambda x: x.fitness_score)

    def select_elite(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Select elite chromosomes from the population"""
        sorted_population = sorted(population, key=lambda x: x.fitness_score, reverse=True)
        return sorted_population[:self.elite_size]

    def evolve_population(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Evolve a population to create the next generation"""
        if not population:
            logger.warning("Empty population provided for evolution")
            return []

        population.sort(key=lambda x: x.fitness_score, reverse=True)
        
        elite = self.select_elite(population)
        new_population = elite.copy()
        
        while len(new_population) < self.population_size:
            parent1 = self.tournament_selection(population)
            parent2 = self.tournament_selection(population)
            
            if parent1 and parent2:
                child1, child2 = self.crossover_chromosomes(parent1, parent2)
                
                # Mutate children
                mutated_child1 = self.mutate_chromosome(child1)
                mutated_child2 = self.mutate_chromosome(child2)
                
                if mutated_child1:
                    new_population.append(mutated_child1)
                if mutated_child2 and len(new_population) < self.population_size:
                    new_population.append(mutated_child2)
        
        logger.info(f"[GENETIC] Evolved population: {len(elite)} elite + {len(new_population) - len(elite)} offspring")
        return new_population[:self.population_size]