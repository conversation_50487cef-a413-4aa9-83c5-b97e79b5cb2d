import asyncio
from datetime import datetime
from typing import Dict, List, Any, Tuple

import torch

from agents.strategy_evolution.evolution_config import EvolutionConfig, StrategyVariant
from agents.strategy_evolution.evolution_logger import logger
from agents.strategy_evolution.strategy_data_loader import StrategyDataLoader
from agents.strategy_evolution.gpu_processing_manager import GPUProcessingManager
from agents.strategy_evolution.performance_tracker import PerformanceTracker
from agents.strategy_evolution.strategy_variant_manager import StrategyVariantManager
from agents.strategy_evolution.strategy_database_manager import StrategyDatabaseManager
from agents.strategy_evolution.yaml_manager import YAMLManager

from agents.strategy_evolution.state_manager import StateManager

class EvolutionCycleManager:
    def __init__(self, evolution_config: EvolutionConfig, data_loader: StrategyDataLoader,
                 gpu_manager: GPUProcessingManager, performance_tracker: PerformanceTracker,
                 variant_manager: StrategyVariantManager, db_manager: StrategyDatabaseManager,
                 yaml_manager: YAMLManager):
        self.evolution_config = evolution_config
        self.data_loader = data_loader
        self.gpu_manager = gpu_manager
        self.performance_tracker = performance_tracker
        self.variant_manager = variant_manager
        self.db_manager = db_manager
        self.yaml_manager = yaml_manager
        self.strategy_processing_semaphore = asyncio.Semaphore(10 if self.gpu_manager.gpu_available else 10) # Max concurrent strategies
        self.state_manager = StateManager()

    async def _run_single_evolution() -> bool:
        """Run single evolution cycle using TRUE GPU parallel processing"""
        try:
            logger.info("🚀 Starting single evolution cycle with TRUE GPU parallel processing")

            self.performance_tracker.start_evolution_cycle()
            self.performance_tracker.reset_stats()

            base_strategies = await self.data_loader.load_base_strategies()
            if not base_strategies:
                return False

            stock_universe = await self.data_loader.discover_stock_universe()
            if not stock_universe:
                logger.error("No stocks found in universe")
                return False

            # Load state and resume if needed
            processed_stocks_count = self.state_manager.load_state()
            if processed_stocks_count > 0:
                logger.info(f"Resuming evolution from {processed_stocks_count} stocks processed.")
                stock_universe = stock_universe[processed_stocks_count:]
                if not stock_universe:
                    logger.info("All stocks already processed. Clearing state and starting new cycle.")
                    self.state_manager.clear_state()
                    return True # Or restart the cycle from beginning

            self.performance_tracker.increment_stocks_tested(len(stock_universe))
            logger.info(f"📊 Discovered {len(stock_universe)} stocks for evolution testing")

            gpu_available = self.gpu_manager.cuda_available

            if gpu_available:
                logger.info(f"🚀 TRUE GPU parallel processing available - {self.gpu_manager.gpu_workers} workers on {self.gpu_manager.device_count} GPUs")
                if torch.cuda.is_available():
                    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                    logger.info(f"💾 GPU Memory: {gpu_memory:.1f} GB")
            else:
                logger.warning("⚠️ GPU not available - using CPU processing")

            all_generated_variants = []
            
            strategy_tasks = []
            current_processed_stocks = processed_stocks_count # Start from loaded state
            for strategy_idx, base_strategy in enumerate(base_strategies):
                # Only process if not already processed
                if strategy_idx >= processed_stocks_count:
                    strategy_tasks.append(
                        self._process_single_strategy_evolution(
                            strategy_idx, base_strategy, stock_universe, len(base_strategies)
                        )
                    )
                    current_processed_stocks += 1
                    self.state_manager.save_state(current_processed_stocks) # Save state after each strategy

            logger.info(f"🚀 Launching {len(strategy_tasks)} concurrent strategy processing tasks")

            results_from_parallel_strategies = await asyncio.gather(*strategy_tasks, return_exceptions=True)

            logger.info("✅ Concurrent strategy processing completed")

            total_variants_generated = 0
            total_variants_above_threshold = 0
            for result in results_from_parallel_strategies:
                if isinstance(result, Exception):
                    logger.error(f"❌ Error in parallel strategy processing: {result}")
                elif result:
                    all_generated_variants.extend(result)
                    for variant in result:
                        total_variants_generated += 1
                        if variant.ranking >= self.evolution_config.min_ranking_threshold:
                            total_variants_above_threshold += 1

            self.performance_tracker.increment_strategies_processed(len(base_strategies))
            self.performance_tracker.increment_variants_generated(total_variants_generated)
            self.performance_tracker.increment_variants_above_threshold(total_variants_above_threshold)

            if all_generated_variants:
                logger.info(f"📄 Saving {len(all_generated_variants)} enhanced strategies to individual files")
                await self.yaml_manager.save_evolved_strategies(all_generated_variants)
                logger.info(f"✅ Successfully saved {len(all_generated_variants)} strategies")
            else:
                logger.warning("⚠️ No quality strategies generated - no files updated")

            self.performance_tracker.increment_variants_added_to_yaml(len(all_generated_variants))

            logger.info(f"✅ Evolution cycle completed - {len(all_generated_variants)} variants generated")
            self.state_manager.clear_state() # Clear state after full cycle
            return True

        except Exception as e:
            logger.error(f"❌ Error in evolution cycle: {e}")
            return False

    async def _process_single_strategy_evolution(self, strategy_idx: int, base_strategy: Dict[str, Any], stock_universe: List[str], total_strategies: int) -> List[StrategyVariant]:
        """
        Process a single base strategy with controlled concurrency to enable true parallel processing.
        This method uses semaphore-controlled resource management for concurrent strategy processing.
        """
        async with self.strategy_processing_semaphore:
            strategy_name = base_strategy.get('name')
            logger.info(f"🧬 Processing strategy {strategy_idx + 1}/{total_strategies}: {strategy_name} (concurrent)")

        stock_timeframe_pairs = []

        gpu_workers = self.gpu_manager.gpu_workers if self.gpu_manager.cuda_available else 4
        gpu_config = self.evolution_config.gpu_config

        stocks_per_worker = gpu_config.get('stocks_per_worker', 2)
        max_stocks_limit = gpu_config.get('max_stocks_per_strategy', None)
        min_stocks = gpu_config.get('min_stocks_per_strategy', 8)

        optimal_stock_count = max(min_stocks, gpu_workers * stocks_per_worker)

        if max_stocks_limit is not None:
            stocks_to_process = min(len(stock_universe), optimal_stock_count, max_stocks_limit)
        else:
            stocks_to_process = min(len(stock_universe), optimal_stock_count)

        logger.info(f"🎯 Using {stocks_to_process} stocks with {gpu_workers} GPU workers (efficiency optimized)")

        for stock_name in stock_universe[:stocks_to_process]:
            for timeframe in base_strategy.get('timeframe', ['1min']):
                stock_timeframe_pairs.append((stock_name, timeframe))
        
        logger.info(f"⚡ TRUE parallel processing {len(stock_timeframe_pairs)} stock-timeframe combinations")
        
        generated_variants_for_this_strategy = []
        try:
            gpu_config = self.evolution_config.gpu_config
            base_timeout = gpu_config.get('batch_timeout_seconds', 60)
            timeout_per_combination = gpu_config.get('timeout_per_combination', 2)

            dynamic_timeout = max(base_timeout, len(stock_timeframe_pairs) * timeout_per_combination)

            strategy_variants = await asyncio.wait_for(
                self.gpu_manager.run_multi_objective_optimization_batch(base_strategy, stock_timeframe_pairs),
                timeout=dynamic_timeout
            )
            
            logger.info(f"✅ Batch processing completed: {len(strategy_variants)} variants generated")
            
            if strategy_variants:
                strategy_variants.sort(key=lambda x: x.ranking, reverse=True)

                quality_variants = [v for v in strategy_variants if v.ranking >= self.evolution_config.min_ranking_threshold]

                if quality_variants:
                    if self.evolution_config.quality_scaling:
                        avg_ranking = sum(v.ranking for v in quality_variants) / len(quality_variants)
                        quality_factor = min(1.0, avg_ranking / 100.0)
                        dynamic_max = max(
                            self.evolution_config.min_variants_per_strategy,
                            int(self.evolution_config.max_variants_per_strategy * quality_factor)
                        )
                        top_variants = quality_variants[:dynamic_max]
                    else:
                        top_variants = quality_variants[:self.evolution_config.max_variants_per_strategy]
                else:
                    top_variants = []

                for variant in top_variants:
                    generated_variants_for_this_strategy.append(variant)
                    await self.db_manager.save_variant_to_database(variant)

                if top_variants:
                    logger.info(f"📝 Added {len(top_variants)} quality variants to enhanced strategies (min ranking: {min(v.ranking for v in top_variants)})")
                else:
                    logger.warning(f"⚠️ No variants met quality threshold (min ranking: {self.evolution_config.min_ranking_threshold}) for {strategy_name}")
            
            gpu_available = self.gpu_manager.cuda_available
            if gpu_available:
                self.gpu_manager.cleanup_gpu_memory()
                
        except asyncio.TimeoutError:
            logger.warning(f"⏰ Batch processing timeout for {strategy_name} - continuing with next strategy")
        except Exception as e:
            logger.error(f"❌ Error in batch processing for {strategy_name}: {e}")
        
        return generated_variants_for_this_strategy
