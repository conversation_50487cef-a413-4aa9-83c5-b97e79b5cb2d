#!/usr/bin/env python3
"""
State Manager Module

This module handles the state of the strategy evolution process, allowing it to be
resumed from where it left off.
"""

import json
from pathlib import Path
from typing import Optional

from agents.strategy_evolution.evolution_logger import logger

class StateManager:
    """
    Manages the state of the evolution process.
    """
    def __init__(self, state_file: str = "data/evolution_state.json"):
        self.state_file = Path(state_file)

    def save_state(self, processed_stocks_count: int):
        """Saves the number of processed stocks to the state file."""
        try:
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump({'processed_stocks_count': processed_stocks_count}, f)
            logger.info(f"[STATE_MGR] Saved evolution state: {processed_stocks_count} stocks processed.")
        except Exception as e:
            logger.error(f"[STATE_MGR] Error saving evolution state: {e}")

    def load_state(self) -> Optional[int]:
        """Loads the number of processed stocks from the state file."""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    processed_stocks_count = data.get('processed_stocks_count', 0)
                    logger.info(f"[STATE_MGR] Loaded evolution state: {processed_stocks_count} stocks processed.")
                    return processed_stocks_count
            return 0
        except Exception as e:
            logger.error(f"[STATE_MGR] Error loading evolution state: {e}")
            return 0

    def clear_state(self):
        """Removes the state file."""
        try:
            if self.state_file.exists():
                self.state_file.unlink()
                logger.info("[STATE_MGR] Cleared evolution state.")
        except Exception as e:
            logger.error(f"[STATE_MGR] Error clearing evolution state: {e}")
