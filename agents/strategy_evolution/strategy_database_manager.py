import sqlite3
import json
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

from agents.strategy_evolution.evolution_config import <PERSON><PERSON><PERSON><PERSON>, StrategyStatus, MarketRegime
from agents.strategy_evolution.evolution_logger import logger

class StrategyDatabaseManager:
    def __init__(self, database_path: str):
        self.database_path = database_path
        self._init_database()

    def _init_database(self):
        """Initialize SQLite database for strategy storage"""
        try:
            Path(self.database_path).parent.mkdir(parents=True, exist_ok=True)
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS strategy_variants (
                        strategy_id TEXT PRIMARY KEY,
                        base_strategy_name TEXT NOT NULL,
                        stock_name TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        ranking INTEGER NOT NULL,
                        entry_conditions TEXT NOT NULL,
                        exit_conditions TEXT NOT NULL,
                        intraday_rules TEXT NOT NULL,
                        risk_reward_ratios TEXT NOT NULL,
                        risk_management TEXT NOT NULL,
                        position_sizing TEXT NOT NULL,
                        performance_metrics TEXT,
                        status TEXT NOT NULL,
                        creation_date TEXT NOT NULL,
                        last_updated TEXT NOT NULL,
                        market_regime TEXT,
                        confidence_score REAL DEFAULT 0.0
                    )
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        metrics TEXT NOT NULL,
                        market_regime TEXT,
                        FOREIGN KEY (strategy_id) REFERENCES strategy_variants (strategy_id)
                    )
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS evolution_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation INTEGER NOT NULL,
                        timestamp TEXT NOT NULL,
                        population_size INTEGER NOT NULL,
                        best_fitness REAL NOT NULL,
                        avg_fitness REAL NOT NULL,
                        convergence_metric REAL NOT NULL,
                        details TEXT
                    )
                ''')
                conn.commit()
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise

    async def save_variant_to_database(self, variant: StrategyVariant):
        """Save strategy variant to database"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO strategy_variants (
                        strategy_id, base_strategy_name, stock_name, timeframe, ranking,
                        entry_conditions, exit_conditions, intraday_rules, risk_reward_ratios,
                        risk_management, position_sizing, performance_metrics, status,
                        creation_date, last_updated, market_regime, confidence_score
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    variant.strategy_id,
                    variant.base_strategy_name,
                    variant.stock_name,
                    variant.timeframe,
                    variant.ranking,
                    json.dumps(variant.entry_conditions),
                    json.dumps(variant.exit_conditions),
                    json.dumps(variant.intraday_rules),
                    json.dumps(variant.risk_reward_ratios),
                    json.dumps(variant.risk_management),
                    json.dumps(variant.position_sizing),
                    json.dumps(variant.performance_metrics),
                    variant.status.value,
                    variant.creation_date.isoformat(),
                    variant.last_updated.isoformat(),
                    variant.market_regime.value if variant.market_regime else None,
                    variant.confidence_score
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Error saving variant to database: {e}")

    async def load_variants_from_database(self) -> List[StrategyVariant]:
        """Load strategy variants from database"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM strategy_variants ORDER BY ranking DESC')
                rows = cursor.fetchall()

            variants = []
            for row in rows:
                try:
                    variant = StrategyVariant(
                        strategy_id=row[0],
                        base_strategy_name=row[1],
                        stock_name=row[2],
                        timeframe=row[3],
                        ranking=row[4],
                        entry_conditions=json.loads(row[5]),
                        exit_conditions=json.loads(row[6]),
                        intraday_rules=json.loads(row[7]),
                        risk_reward_ratios=json.loads(row[8]),
                        risk_management=json.loads(row[9]),
                        position_sizing=json.loads(row[10]),
                        performance_metrics=json.loads(row[11]) if row[11] else {},
                        status=StrategyStatus(row[12]),
                        creation_date=datetime.fromisoformat(row[13]),
                        last_updated=datetime.fromisoformat(row[14]),
                        market_regime=MarketRegime(row[15]) if row[15] else None,
                        confidence_score=row[16]
                    )
                    variants.append(variant)
                except json.JSONDecodeError as e:
                    logger.error(f"JSONDecodeError loading variant from DB (ID: {row[0]}): {e}. Raw performance_metrics: {row[11]}")
                except Exception as e:
                    logger.error(f"Error loading variant from DB (ID: {row[0]}): {e}. Raw row data: {row}")

            logger.info(f"📊 Loaded {len(variants)} variants from database")
            return variants

        except Exception as e:
            logger.error(f"Error loading variants from database: {e}")
            return []
