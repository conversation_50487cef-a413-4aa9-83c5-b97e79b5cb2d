import asyncio
import json
import yaml
import polars as pl
import numpy as np
import torch
import uuid
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime

from agents.enhanced_backtesting_kimi import run_backtesting_for_evolution
from utils.real_gpu_accelerator import real_gpu_accelerator
from utils.gpu_hyperopt_free import gpu_hyperopt_free
from utils.gpu_parallel_processor import gpu_parallel_processor, GPUTask

from agents.strategy_evolution.evolution_config import EvolutionConfig, StrategyVariant, StrategyStatus
from agents.strategy_evolution.evolution_logger import logger
from agents.strategy_evolution.strategy_evaluator import StrategyEvaluator
from agents.strategy_evolution.genetic_algorithm import GeneticAlgorithm
from agents.strategy_evolution.strategy_variant_manager import StrategyVariantManager

class StrategyOptimizer:
    """
    Handles the multi-objective optimization and genetic algorithm operations for strategy evolution.
    """
    def __init__(self, evolution_config: EvolutionConfig, strategy_evaluator: StrategyEvaluator):
        self.evolution_config = evolution_config
        self.strategy_evaluator = strategy_evaluator
        self.genetic_algorithm = GeneticAlgorithm(evolution_config)
        self.variant_manager = StrategyVariantManager(evolution_config)

    def _evaluate_strategy_fitness_sync_with_params(self, base_strategy: Dict[str, Any],
                                                  stock_name: str, timeframe: str,
                                                  params: Dict[str, Any]) -> float:
        """
        Evaluate strategy fitness with specific parameters for free GPU optimization
        """
        try:
            # Create strategy variant with optimized parameters
            strategy_variant = StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_strategy['name'], # Changed from strategy_name to base_strategy_name
                stock_name=stock_name,
                timeframe=timeframe,
                # Assuming 'parameters' is not directly part of StrategyVariant,
                # but its components are mapped to entry_conditions, risk_management etc.
                entry_conditions={
                    'oversold_threshold': params.get('oversold_threshold', 30),
                    'overbought_threshold': params.get('overbought_threshold', 70),
                    'rsi_period': params.get('rsi_period', 14)
                },
                risk_reward_ratios=[params.get('risk_reward_ratio', [1.0, 2.0])],
                risk_management={
                    'stop_loss': params.get('stop_loss', 0.02),
                    'take_profit': params.get('take_profit', 0.04),
                    'max_position_size': params.get('max_position_size', 0.1)
                },
                position_sizing={
                    'risk_per_trade': params.get('risk_per_trade', 0.02),
                    'max_trades': params.get('max_trades', 3)
                },
                performance_metrics={}
            )

            # Evaluate fitness using the provided StrategyEvaluator
            fitness_metrics = self.strategy_evaluator._evaluate_strategy_fitness_sync(strategy_variant)
            return fitness_metrics.get('composite_score', 0.0)

        except Exception as e:
            logger.warning(f"Parameter evaluation failed: {e}")
            return 0.0

    def _get_parameter_space(self, base_strategy: Dict[str, Any]) -> Dict[str, Any]:
        return self.variant_manager.get_parameter_space(base_strategy)

    async def run_multi_objective_optimization_batch(self, base_strategy: Dict[str, Any],
                                                   stock_timeframe_pairs: List[Tuple[str, str]],
                                                   evolution_stats: Dict[str, Any]) -> List[StrategyVariant]:
        """
        Run multi-objective optimization for multiple stock-timeframe combinations in TRUE parallel
        """
        try:
            strategy_name = base_strategy['name']
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            logger.info(f"🚀 TRUE GPU Parallel optimization for {strategy_name} on {len(stock_timeframe_pairs)} combinations")
            
            gpu_available = gpu_parallel_processor.cuda_available
            all_variants = []
            
            if not gpu_available:
                logger.warning("GPU not available. Skipping parallel optimization.")
                return []

            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            logger.info(f"⚡ Using TRUE GPU parallel processing with {gpu_parallel_processor.gpu_workers} workers")
            
            gpu_tasks = []
            for i, (stock_name, timeframe) in enumerate(stock_timeframe_pairs):
                stock_files = list(Path("data/features").glob(f"features_{stock_name}_*.parquet"))
                if stock_files:
                    try:
                        df = pl.read_parquet(stock_files[0])
                        if len(df) >= 100:
                            data_arrays = {
                                'close': df['close'].to_numpy(),
                                'high': df['high'].to_numpy() if 'high' in df.columns else df['close'].to_numpy(),
                                'low': df['low'].to_numpy() if 'low' in df.columns else df['close'].to_numpy(),
                                'volume': df['volume'].to_numpy() if 'volume' in df.columns else np.ones(len(df))
                            }
                            
                            gpu_config = self.evolution_config.gpu_config
                            variants_per_stock = gpu_config.get('variants_per_stock', 3)

                            strategies = []
                            for variant_idx in range(variants_per_stock):
                                strategies.append({
                                    'name': f"{base_strategy['name']}_{stock_name}_v{variant_idx}",
                                    'type': base_strategy['name'],
                                    'stock_name': stock_name,
                                    'timeframe': timeframe,
                                    'variant_idx': variant_idx
                                })
                            
                            task = GPUTask(
                                task_id=f"{stock_name}_{timeframe}_{i}",
                                data=data_arrays,
                                strategies=strategies
                            )
                            gpu_tasks.append(task)
                            
                    except Exception as e:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        logger.warning(f"⚠️ Failed to load data for {stock_name}: {e}")
                        continue
            
            if gpu_tasks:
                gpu_config = self.evolution_config.gpu_config
                max_batch_size = gpu_config.get('max_batch_size', 32)

                if len(gpu_tasks) > max_batch_size:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    logger.info(f"🔄 Large batch detected: {len(gpu_tasks)} tasks, processing in chunks of {max_batch_size}")

                    all_parallel_results = []
                    for i in range(0, len(gpu_tasks), max_batch_size):
                        batch = gpu_tasks[i:i + max_batch_size]
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        logger.info(f"🔥 Processing batch {i//max_batch_size + 1}: {len(batch)} tasks")

                        batch_results = await gpu_parallel_processor.process_batch_parallel(batch)
                        all_parallel_results.extend(batch_results)

                        cleanup_task = asyncio.create_task(gpu_parallel_processor.cleanup_gpu_memory_async())
                        await asyncio.sleep(0.1)
                        await cleanup_task

                    parallel_results = all_parallel_results
                else:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    logger.info(f"🔥 Processing {len(gpu_tasks)} tasks in TRUE parallel on GPU")
                    parallel_results = await gpu_parallel_processor.process_batch_parallel(gpu_tasks)
                
                for result in parallel_results:
                    if 'error' not in result['result']:
                        task_result = result['result']
                        signals = task_result.get('signals', {})
                        backtest = task_result.get('backtest', {})
                        
                        for strategy_name, signal_array in signals.items():
                            parts = strategy_name.split('_')
                            if len(parts) >= 3:
                                stock_name = parts[1]
                                variant_idx = int(parts[-1][1:]) if parts[-1].startswith('v') else 0
                                
                                variant = self._create_variant_from_gpu_results(
                                    base_strategy, stock_name, '1min', 
                                    backtest.get(strategy_name, {}), variant_idx
                                )
                                
                                if strategy_name in backtest:
                                    gpu_metrics = backtest[strategy_name]
                                    fitness_metrics = {
                                        'sharpe_ratio': gpu_metrics.get('sharpe_ratio', 0.0),
                                        'max_drawdown': gpu_metrics.get('max_drawdown', 20.0),
                                        'win_rate': gpu_metrics.get('win_rate', 0.5),
                                        'total_trades': gpu_metrics.get('total_trades', 1),
                                        'total_pnl': gpu_metrics.get('total_pnl', 0.0),
                                        'roi': gpu_metrics.get('roi', 0.0)
                                    }
                                    
                                    composite_score = self.strategy_evaluator._calculate_composite_fitness(fitness_metrics)
                                    fitness_metrics['composite_score'] = composite_score
                                    
                                    variant.ranking = max(10, int(composite_score * 100))
                                    variant.performance_metrics = fitness_metrics
                                    
                                    if variant.ranking >= self.evolution_config.min_ranking_threshold:
                                        all_variants.append(variant)
                
                asyncio.create_task(gpu_parallel_processor.cleanup_gpu_memory_async())

                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                logger.info(f"✅ TRUE GPU parallel processing completed - {len(all_variants)} variants generated")
            else:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                logger.warning("⚠️ No valid GPU tasks created")
            
            return all_variants
            
        except Exception as e:
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            logger.error(f"❌ Error in batch multi-objective optimization: {e}")
            logger.error(f"Error in batch multi-objective optimization: {e}")
            return []
    
    def _create_variant_from_gpu_results(self, base_strategy, stock_name, timeframe, gpu_result, variant_idx):
        """Create variant from GPU results with parameter variations"""
        stop_loss = 0.015 + (variant_idx * 0.005)
        take_profit = stop_loss * 2
        
        return StrategyVariant(
            strategy_id=str(uuid.uuid4()),
            base_strategy_name=base_strategy['name'],
            stock_name=stock_name,
            timeframe=timeframe,
            ranking=100,
            entry_conditions={
                'oversold_threshold': 25 + (variant_idx * 5),
                'overbought_threshold': 75 - (variant_idx * 5)
            },
            exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
            intraday_rules={},
            risk_reward_ratios=[[1, 2]],
            risk_management={'stop_loss': stop_loss, 'take_profit': take_profit},
            position_sizing={'risk_per_trade': 0.02}
        )

    async def run_multi_objective_optimization(self, base_strategy: Dict[str, Any],
                                             stock_name: str, timeframe: str,
                                             evolution_stats: Dict[str, Any]) -> List[StrategyVariant]:
        """
        Run GPU-accelerated optimization using custom GPU hyperparameter optimizer
        """
        try:
            evolution_stats['current_stock'] = stock_name
            evolution_stats['current_strategy'] = base_strategy['name']

            logger.stock_progress(stock_name, f"GPU optimizing {base_strategy['name']} ({timeframe})")

            optimization_result = gpu_hyperopt_free.optimize_strategy_parameters(
                objective_func=lambda params: self._evaluate_strategy_fitness_sync_with_params(
                    base_strategy, stock_name, timeframe, params
                ),
                param_space=self._get_parameter_space(base_strategy),
                n_trials=50,
                method='auto'
            )

            evolution_stats['optimization_tasks_completed'] += 1

            optimized_variants = []
            if optimization_result.best_score > 0.1:
                best_params = optimization_result.best_params
                
                variant = StrategyVariant(
                    strategy_id=str(uuid.uuid4()),
                    base_strategy_name=base_strategy['name'],
                    stock_name=stock_name,
                    timeframe=timeframe,
                    ranking=100,
                    entry_conditions={
                        'oversold_threshold': best_params.get('oversold_threshold', 30),
                        'overbought_threshold': best_params.get('overbought_threshold', 70),
                        'rsi_period': best_params.get('rsi_period', 14)
                    },
                    exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
                    intraday_rules={},
                    risk_reward_ratios=[best_params.get('risk_reward_ratio', [1, 2])],
                    risk_management={
                        'stop_loss': best_params.get('stop_loss', 0.02),
                        'take_profit': best_params.get('take_profit', 0.04),
                        'max_position_size': best_params.get('max_position_size', 0.1)
                    },
                    position_sizing={
                        'risk_per_trade': best_params.get('risk_per_trade', 0.02),
                        'max_trades': best_params.get('max_trades', 3)
                    }
                )
                
                fitness_metrics = {
                    'sharpe_ratio': optimization_result.best_score,
                    'max_drawdown': 20.0,
                    'win_rate': 0.6,
                    'total_trades': 10,
                    'total_pnl': optimization_result.best_score * 100,
                    'roi': optimization_result.best_score * 10,
                    'composite_score': optimization_result.best_score
                }
                
                variant.ranking = max(10, int(optimization_result.best_score * 100))
                variant.performance_metrics = fitness_metrics
                
                if variant.ranking >= self.evolution_config.min_ranking_threshold:
                    optimized_variants.append(variant)

            logger.stock_progress(stock_name, f"GPU generated {len(optimized_variants)} variants (score: {optimization_result.best_score:.3f})")

            evolution_stats['variants_generated'] += len(optimized_variants)
            evolution_stats['variants_above_threshold'] += len(optimized_variants)
            
            gpu_hyperopt_free.cleanup_gpu_memory()

            return optimized_variants

        except Exception as e:
            logger.error(f"Error in GPU multi-objective optimization: {e}")
            evolution_stats['optimization_tasks_failed'] += 1
            return []