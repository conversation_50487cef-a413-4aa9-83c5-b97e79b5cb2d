#!/usr/bin/env python3
"""
Enhanced Strategy Evolution Agent - Main Agent Class
"""

import asyncio
from pathlib import Path
from typing import Dict, List, Any

from agents.strategy_evolution.evolution_config import EvolutionConfig, StrategyVariant
from agents.strategy_evolution.evolution_logger import logger
from agents.strategy_evolution.strategy_database_manager import StrategyDatabaseManager
from agents.strategy_evolution.strategy_fitness_evaluator import StrategyFitnessEvaluator
from agents.strategy_evolution.strategy_evaluator import StrategyEvaluator
from agents.strategy_evolution.strategy_data_loader import StrategyDataLoader
from agents.strategy_evolution.strategy_optimizer import StrategyOptimizer
from agents.strategy_evolution.genetic_algorithm import GeneticAlgorithm
from agents.strategy_evolution.strategy_variant_manager import StrategyVariantManager
from agents.strategy_evolution.yaml_manager import YAMLManager
from agents.strategy_evolution.lifecycle_manager import LifecycleManager
from agents.strategy_evolution.gpu_processing_manager import GPUProcessingManager
from agents.strategy_evolution.performance_tracker import PerformanceTracker
from agents.strategy_evolution.fast_path_optimizer import FastPathOptimizer
from agents.strategy_evolution.strategy_optimizer_manager import StrategyOptimizerManager
from agents.strategy_evolution.evolution_cycle_manager import EvolutionCycleManager
from agents.strategy_evolution.integration import PerformancePredictionHandler

class EnhancedStrategyEvolutionAgent:
    """
    Enhanced Strategy Evolution Agent with comprehensive optimization capabilities.
    """

    def __init__(self, config_path: str = "agents/config/enhanced_strategy_evolution_config.yaml"):
        """Initialize Enhanced Strategy Evolution Agent."""
        self.config_path = config_path

        # Streamlined configuration loading
        raw_config = YAMLManager.load_raw_config(config_path)
        evolution_config_data = raw_config.get('evolution', {})
        gpu_config = raw_config.get('gpu', {})
        if gpu_config:
            evolution_config_data['gpu_config'] = gpu_config

        # Add new configurable fields for integration
        integration_config = raw_config.get('integration', {})
        evolution_config_data['performance_analysis_gateway_endpoint'] = integration_config.get('performance_analysis_gateway_endpoint', 'http://localhost:8088')
        evolution_config_data['performance_analysis_callback_path'] = integration_config.get('performance_analysis_callback_path', '/performance_prediction')
        evolution_config_data['generation_interval_seconds'] = evolution_config_data.get('generation_interval_seconds', 3600)


        self.evolution_config = EvolutionConfig(**evolution_config_data)
        self.config = raw_config

        # Initialize YAML manager once with the full config
        self.yaml_manager = YAMLManager(self.evolution_config)

        # Lazy-load SignalAgent only when needed
        self._signal_agent = None

        self.database_path = self.evolution_config.storage_config["database_path"]
        self.db_manager = StrategyDatabaseManager(self.database_path)

        self.data_loader = StrategyDataLoader(self.evolution_config)
        self.variant_manager = StrategyVariantManager(self.evolution_config)
        self.gpu_manager = GPUProcessingManager(self.evolution_config)
        self.strategy_fitness_evaluator = StrategyFitnessEvaluator(self.evolution_config, self.variant_manager, self.gpu_manager)
        self.strategy_optimizer_manager = StrategyOptimizerManager(self.evolution_config, self.variant_manager, self.gpu_manager, self.strategy_fitness_evaluator)
        self.strategy_evaluator = StrategyEvaluator(self.evolution_config)
        self.strategy_optimizer = StrategyOptimizer(self.evolution_config, self.strategy_evaluator)

        self.genetic_algorithm = GeneticAlgorithm(self.evolution_config)
        self.lifecycle_manager = LifecycleManager(self.evolution_config, self.database_path)
        self.performance_tracker = PerformanceTracker(self.evolution_config)
        self.fast_path_optimizer = FastPathOptimizer(self.evolution_config, self.gpu_manager.calculate_composite_fitness)
        self.evolution_cycle_manager = EvolutionCycleManager(
            self.evolution_config, self.data_loader, self.gpu_manager, self.performance_tracker,
            self.variant_manager, self.db_manager, self.yaml_manager
        )

        self.performance_prediction_handler = PerformancePredictionHandler(self)

        self.active_variants: Dict[str, StrategyVariant] = {}
        self.generation_counter = 0
        self.is_running = False
        self.evolution_history: List[Dict[str, Any]] = []

        logger.info(f"[INIT] Strategy-level parallelism: {self.evolution_cycle_manager.strategy_processing_semaphore._value} concurrent strategies")
        self._validate_initialization()
        logger.info("[INIT] EnhancedStrategyEvolutionAgent initialized successfully.")

    @property
    def signal_agent(self):
        """Lazy-load the SignalAgent."""
        if self._signal_agent is None:
            from agents.signal_generation.signal_agent import SignalAgent
            self._signal_agent = SignalAgent()
        return self._signal_agent

    def _validate_initialization(self):
        """Validate that all required components are properly initialized."""
        try:
            required_dirs = ['data/features', 'logs', 'agents/config']
            for dir_path in required_dirs:
                Path(dir_path).mkdir(parents=True, exist_ok=True)

            if not self.config:
                raise ValueError("Configuration is empty or invalid")
            if not hasattr(self, 'evolution_config') or not self.evolution_config:
                raise ValueError("Evolution configuration is missing or invalid")

            strategies_path = Path("agents/config/strategies.yaml")
            if not strategies_path.exists():
                logger.warning("strategies.yaml not found - will be created during evolution")

            logger.info("✅ Initialization validation completed successfully")
        except Exception as e:
            logger.error(f"Initialization validation failed: {e}")
            raise RuntimeError(f"Agent validation failed: {e}") from e

    async def start_evolution_process(self) -> bool:
        """Start the main evolution process."""
        try:
            logger.info("🚀 Starting Enhanced Strategy Evolution Process")
            self.is_running = True

            while self.is_running and self.generation_counter < self.evolution_config.max_generations:
                logger.info(f"🧬 Generation {self.generation_counter + 1}")

                # This is a placeholder for the actual evolution logic which seems to be missing
                # from the original file. Based on the file structure, `enhance_strategies_yaml`
                # and `manage_strategy_lifecycle` should be here.
                # success = await self.enhance_strategies_yaml()
                # if not success:
                #     logger.error("Strategy enhancement failed")
                #     break
                # await self.manage_strategy_lifecycle()

                self.generation_counter += 1
                # No sleep for continuous processing

            logger.info("✅ Evolution process completed")
            return True
        except Exception as e:
            logger.error(f"Error in evolution process: {e}")
            return False

    def stop_evolution_process(self):
        """Stop the evolution process."""
        self.is_running = False
        logger.info("🛑 Evolution process stopped")

    async def handle_performance_prediction(self, prediction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle performance prediction from Performance Analysis Gateway."""
        return await self.performance_prediction_handler.handle_performance_prediction(prediction_data)

    async def register_with_performance_analysis_gateway(self, gateway_endpoint: str) -> bool:
        """Register with Performance Analysis Gateway for predictions."""
        try:
            import aiohttp

            registration_data = {
                'agent_type': 'strategy_evolution',
                'agent_id': 'enhanced_strategy_evolution_agent',
                'callback_endpoint': f"{self.evolution_config.performance_analysis_gateway_endpoint}{self.evolution_config.performance_analysis_callback_path}",
                'prediction_types': [
                    'sharpe_ratio', 'roi', 'drawdown', 'profitability', 'win_rate', 'volatility'
                ]
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{gateway_endpoint}/register_agent",
                    json=registration_data,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        logger.info("Successfully registered with Performance Analysis Gateway")
                        return True
                    else:
                        logger.error(f"Failed to register with Performance Analysis Gateway: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error registering with Performance Analysis Gateway: {e}")
            return False

    def get_optimization_guidance_summary(self) -> Dict[str, Any]:
        """Get summary of current optimization guidance."""
        return self.performance_prediction_handler.get_prediction_handler_status()

    def incorporate_predictions_into_fitness(self, strategy_id: str, base_fitness: float) -> float:
        """Incorporate performance predictions into fitness calculation."""
        return self.performance_prediction_handler.incorporate_predictions_into_fitness(strategy_id, base_fitness)
