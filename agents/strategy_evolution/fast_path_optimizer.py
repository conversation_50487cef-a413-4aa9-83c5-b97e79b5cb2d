import uuid
import numpy as np
from typing import Dict, List, Any, Callable
from datetime import datetime
from pathlib import Path

from agents.strategy_evolution.evolution_config import EvolutionConfig, StrategyVariant
from agents.strategy_evolution.evolution_logger import logger
from utils.real_gpu_accelerator import real_gpu_accelerator
import polars as pl

class FastPathOptimizer:
    """
    Implements lightweight intelligent sampling for the "fast" GPU path.
    Generates diverse and genuinely optimized variants using real data and direct GPU backtesting.
    """

    def __init__(self, evolution_config: EvolutionConfig, composite_fitness_calculator: Callable[[Dict[str, float]], float]):
        self.evolution_config = evolution_config
        self.gpu_config = evolution_config.gpu_config
        self._calculate_composite_fitness = composite_fitness_calculator

    async def generate_fast_variants(self, stock_name: str, timeframe: str, base_strategy: Dict[str, Any]) -> List[StrategyVariant]:
        """
        Generates strategy variants using lightweight intelligent sampling and direct GPU backtesting.
        Ensures real data and logic are used, and throws errors on failure.
        """
        try:
            # Load real market data for the specific stock
            stock_files = list(Path("data/features").glob(f"features_{stock_name}_*.parquet"))
            if not stock_files:
                raise ValueError(f"No real data found for {stock_name} in data/features. Cannot perform fast path optimization.")
            
            df = pl.read_parquet(stock_files[0])
            
            # Ensure sufficient data for meaningful evaluation
            if len(df) < 100:
                raise ValueError(f"Insufficient real data for {stock_name} ({len(df)} rows). Cannot perform fast path optimization.")
            
            # Convert to numpy arrays for GPU processing
            data_arrays = {
                'close': df['close'].to_numpy(),
                'high': df['high'].to_numpy() if 'high' in df.columns else df['close'].to_numpy(),
                'low': df['low'].to_numpy() if 'low' in df.columns else df['close'].to_numpy(),
                'volume': df['volume'].to_numpy() if 'volume' in df.columns else np.ones(len(df))
            }
            
            # Implement lightweight intelligent sampling (e.g., simplified grid search or random sampling)
            # Define parameter ranges for sampling based on typical strategy parameters
            stop_loss_range = np.linspace(0.01, 0.03, self.gpu_config.get('fast_path_stop_loss_samples', 3))
            take_profit_multiplier_range = np.linspace(1.5, 2.5, self.gpu_config.get('fast_path_take_profit_samples', 3))
            oversold_threshold_range = np.linspace(20, 30, self.gpu_config.get('fast_path_oversold_samples', 3))
            overbought_threshold_range = np.linspace(70, 80, self.gpu_config.get('fast_path_overbought_samples', 3))

            sampled_params_list = []
            for sl in stop_loss_range:
                for tp_mult in take_profit_multiplier_range:
                    for os_thresh in oversold_threshold_range:
                        for ob_thresh in overbought_threshold_range:
                            sampled_params_list.append({
                                'stop_loss': sl,
                                'take_profit': sl * tp_mult,
                                'oversold_threshold': os_thresh,
                                'overbought_threshold': ob_thresh
                            })
            
            # Limit the number of samples for "fast" path
            max_fast_samples = self.gpu_config.get('max_fast_path_samples', 10)
            if len(sampled_params_list) > max_fast_samples:
                # Randomly sample if too many combinations
                np.random.shuffle(sampled_params_list)
                sampled_params_list = sampled_params_list[:max_fast_samples]

            generated_variants = []

            for params in sampled_params_list:
                # Create a temporary strategy config for GPU backtesting
                strategy_config_for_gpu = {
                    'name': base_strategy['name'],
                    'type': base_strategy['name'],
                    'parameters': {
                        'stop_loss': params['stop_loss'],
                        'take_profit': params['take_profit'],
                        'entry_conditions': {
                            'oversold_threshold': params['oversold_threshold'],
                            'overbought_threshold': params['overbought_threshold']
                        }
                    }
                }
                
                # Process on GPU using real GPU accelerator
                gpu_results = real_gpu_accelerator.vectorized_backtest_gpu(data_arrays, [strategy_config_for_gpu])
                
                if not gpu_results or not gpu_results[0]:
                    logger.warning(f"GPU backtesting returned no valid results for {stock_name}-{timeframe} with params {params}. Skipping variant.")
                    continue # Skip this variant if GPU results are not valid

                result = gpu_results[0] # Take the first result for this strategy config

                # Create StrategyVariant from the sampled parameters and GPU results
                variant = StrategyVariant(
                    strategy_id=str(uuid.uuid4()),
                    base_strategy_name=base_strategy['name'],
                    stock_name=stock_name,
                    timeframe=timeframe,
                    ranking=0, # Will be updated
                    entry_conditions={
                        'oversold_threshold': params['oversold_threshold'],
                        'overbought_threshold': params['overbought_threshold']
                    },
                    exit_conditions=base_strategy.get('exit_conditions', {}),
                    intraday_rules=base_strategy.get('intraday_rules', {}),
                    risk_reward_ratios=base_strategy.get('risk_reward_ratios', [[1, 2]]),
                    risk_management={
                        'stop_loss': params['stop_loss'],
                        'take_profit': params['take_profit']
                    },
                    position_sizing=base_strategy.get('position_sizing', {'risk_per_trade': 0.02}),
                    performance_metrics={}, # Will be updated
                    creation_date=datetime.now(),
                    last_updated=datetime.now()
                )

                # Directly use GPU results for fitness metrics
                fitness_metrics = {
                    'sharpe_ratio': result.get('sharpe_ratio', 0.0),
                    'max_drawdown': result.get('max_drawdown', 100.0),
                    'win_rate': result.get('win_rate', 0.0),
                    'total_trades': result.get('total_trades', 0),
                    'total_pnl': result.get('total_pnl', 0.0),
                    'roi': result.get('roi', 0.0)
                }
                
                # Calculate composite fitness score using the provided calculator
                composite_score = self._calculate_composite_fitness(fitness_metrics)
                fitness_metrics['composite_score'] = composite_score

                # Calculate ranking based on composite score
                scaled_score = 20 + (composite_score * 60)  # Maps 0.0-1.0 to 20-80
                variant.ranking = max(15, min(85, int(scaled_score)))
                variant.performance_metrics = fitness_metrics

                if variant.ranking >= self.evolution_config.min_ranking_threshold:
                    generated_variants.append(variant)
            
            logger.info(f"Generated {len(generated_variants)} fast path variants for {stock_name}-{timeframe}")
            return generated_variants
            
        except Exception as e:
            logger.error(f"Error in fast path optimization for {stock_name}-{timeframe}: {e}")
            # Re-raise the exception to ensure failures are not silently ignored
            raise
