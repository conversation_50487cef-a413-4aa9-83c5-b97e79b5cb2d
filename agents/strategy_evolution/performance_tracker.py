#!/usr/bin/env python3
"""
Performance Tracker Module

This module handles performance tracking and statistics for strategy evolution including:
- Evolution statistics tracking
- Progress monitoring
- Performance summaries
- Real-time metrics updates
- Historical performance analysis
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from agents.strategy_evolution.evolution_logger import logger


class PerformanceTracker:
    """
    Tracks and manages performance statistics for strategy evolution
    """
    
    def __init__(self, evolution_config):
        self.evolution_config = evolution_config
        
        # Evolution progress tracking
        self.evolution_stats = {
            'stocks_tested': 0,
            'strategies_processed': 0,
            'variants_generated': 0,
            'variants_above_threshold': 0,
            'variants_added_to_yaml': 0,
            'optimization_tasks_completed': 0,
            'optimization_tasks_failed': 0,
            'start_time': None,
            'current_stock': None,
            'current_strategy': None,
            'generation_counter': 0,
            'total_processing_time': 0.0,
            'average_time_per_stock': 0.0,
            'gpu_utilization_time': 0.0,
            'cpu_fallback_time': 0.0
        }
        
        # Performance tracking
        self.performance_tracker = {}
        self.regime_adaptations = {}
        
        # Historical data
        self.generation_history = []
        self.performance_history = []
        
        logger.info("[PERF_TRACKER] Performance Tracker initialized")
    
    def start_evolution_cycle(self):
        """Start tracking a new evolution cycle"""
        self.evolution_stats['start_time'] = datetime.now()
        self.evolution_stats['generation_counter'] += 1
        
        logger.info(f"🚀 Started evolution cycle #{self.evolution_stats['generation_counter']}")
    
    def update_stock_progress(self, stock_name: str, strategy_name: str = None):
        """Update current stock being processed"""
        self.evolution_stats['current_stock'] = stock_name
        if strategy_name:
            self.evolution_stats['current_strategy'] = strategy_name
    
    def increment_stocks_tested(self, count: int = 1):
        """Increment the number of stocks tested"""
        self.evolution_stats['stocks_tested'] += count
    
    def increment_strategies_processed(self, count: int = 1):
        """Increment the number of strategies processed"""
        self.evolution_stats['strategies_processed'] += count
    
    def increment_variants_generated(self, count: int = 1):
        """Increment the number of variants generated"""
        self.evolution_stats['variants_generated'] += count
    
    def increment_variants_above_threshold(self, count: int = 1):
        """Increment the number of variants above threshold"""
        self.evolution_stats['variants_above_threshold'] += count
    
    def increment_variants_added_to_yaml(self, count: int = 1):
        """Increment the number of variants added to YAML"""
        self.evolution_stats['variants_added_to_yaml'] += count
    
    def increment_optimization_completed(self, count: int = 1):
        """Increment completed optimization tasks"""
        self.evolution_stats['optimization_tasks_completed'] += count
    
    def increment_optimization_failed(self, count: int = 1):
        """Increment failed optimization tasks"""
        self.evolution_stats['optimization_tasks_failed'] += count
    
    def add_processing_time(self, processing_time: float, is_gpu: bool = False):
        """Add processing time to statistics"""
        self.evolution_stats['total_processing_time'] += processing_time
        
        if is_gpu:
            self.evolution_stats['gpu_utilization_time'] += processing_time
        else:
            self.evolution_stats['cpu_fallback_time'] += processing_time
        
        # Update average time per stock
        if self.evolution_stats['stocks_tested'] > 0:
            self.evolution_stats['average_time_per_stock'] = (
                self.evolution_stats['total_processing_time'] / self.evolution_stats['stocks_tested']
            )
    
    def get_evolution_stats(self) -> Dict[str, Any]:
        """Get current evolution statistics"""
        return self.evolution_stats.copy()
    
    def get_success_rates(self) -> Dict[str, float]:
        """Calculate various success rates"""
        try:
            stats = self.evolution_stats
            
            # Optimization success rate
            total_tasks = stats['optimization_tasks_completed'] + stats['optimization_tasks_failed']
            optimization_success_rate = (stats['optimization_tasks_completed'] / total_tasks * 100) if total_tasks > 0 else 0
            
            # Threshold success rate
            threshold_rate = (stats['variants_above_threshold'] / stats['variants_generated'] * 100) if stats['variants_generated'] > 0 else 0
            
            # YAML addition rate
            yaml_addition_rate = (stats['variants_added_to_yaml'] / stats['variants_above_threshold'] * 100) if stats['variants_above_threshold'] > 0 else 0
            
            # GPU utilization rate
            gpu_utilization_rate = (stats['gpu_utilization_time'] / stats['total_processing_time'] * 100) if stats['total_processing_time'] > 0 else 0
            
            return {
                'optimization_success_rate': optimization_success_rate,
                'threshold_success_rate': threshold_rate,
                'yaml_addition_rate': yaml_addition_rate,
                'gpu_utilization_rate': gpu_utilization_rate
            }
            
        except Exception as e:
            logger.error(f"Error calculating success rates: {e}")
            return {}
    
    def print_evolution_summary(self):
        """Print comprehensive evolution summary"""
        try:
            stats = self.evolution_stats

            # Calculate elapsed time
            if stats['start_time']:
                elapsed_time = datetime.now() - stats['start_time']
                elapsed_str = f"{elapsed_time.total_seconds():.1f}s"
            else:
                elapsed_str = "N/A"

            # Calculate success rates
            success_rates = self.get_success_rates()

            logger.info("\n" + "="*80)
            logger.info("📊 EVOLUTION SUMMARY")
            logger.info("="*80)
            logger.info(f"⏱️  Elapsed Time: {elapsed_str}")
            logger.info(f"🔄 Generation: #{stats['generation_counter']}")
            logger.info(f"🏢 Stocks Tested: {stats['stocks_tested']}")
            logger.info(f"🧬 Strategies Processed: {stats['strategies_processed']}")
            logger.info(f"⚡ Optimization Tasks: {stats['optimization_tasks_completed']}/{stats['optimization_tasks_completed'] + stats['optimization_tasks_failed']} ({success_rates.get('optimization_success_rate', 0):.1f}% success)")
            logger.info(f"🎯 Variants Generated: {stats['variants_generated']}")
            logger.info(f"✅ Above Threshold: {stats['variants_above_threshold']} ({success_rates.get('threshold_success_rate', 0):.1f}%)")
            logger.info(f"📝 Added to YAML: {stats['variants_added_to_yaml']}")
            logger.info(f"⚡ GPU Utilization: {success_rates.get('gpu_utilization_rate', 0):.1f}%")
            logger.info(f"⏱️  Avg Time/Stock: {stats['average_time_per_stock']:.2f}s")

            if stats['current_stock'] and stats['current_strategy']:
                logger.info(f"🔄 Currently Processing: {stats['current_stock']} - {stats['current_strategy']}")

            logger.info("="*80)

            # Log to file as well
            logger.info(f"[SUMMARY] Evolution Summary - Generation: {stats['generation_counter']}, "
                       f"Stocks: {stats['stocks_tested']}, "
                       f"Strategies: {stats['strategies_processed']}, "
                       f"Variants: {stats['variants_generated']}, "
                       f"Above Threshold: {stats['variants_above_threshold']}, "
                       f"Added to YAML: {stats['variants_added_to_yaml']}")
                       
        except Exception as e:
            logger.error(f"Error printing evolution summary: {e}")
    
    def save_generation_snapshot(self):
        """Save a snapshot of the current generation's performance"""
        try:
            snapshot = {
                'generation': self.evolution_stats['generation_counter'],
                'timestamp': datetime.now().isoformat(),
                'stats': self.evolution_stats.copy(),
                'success_rates': self.get_success_rates()
            }
            
            self.generation_history.append(snapshot)
            
            # Keep only last 100 generations
            if len(self.generation_history) > 100:
                self.generation_history = self.generation_history[-100:]
                
            logger.debug(f"Saved generation snapshot for generation {snapshot['generation']}")
            
        except Exception as e:
            logger.error(f"Error saving generation snapshot: {e}")
    
    def get_generation_history(self, last_n: int = 10) -> List[Dict[str, Any]]:
        """Get history of last N generations"""
        try:
            return self.generation_history[-last_n:] if self.generation_history else []
        except Exception as e:
            logger.error(f"Error getting generation history: {e}")
            return []
    
    def calculate_performance_trends(self) -> Dict[str, float]:
        """Calculate performance trends over recent generations"""
        try:
            if len(self.generation_history) < 2:
                return {}
            
            recent_history = self.generation_history[-10:]  # Last 10 generations
            
            trends = {}
            
            # Calculate trends for key metrics
            metrics_to_track = [
                'variants_generated',
                'variants_above_threshold', 
                'variants_added_to_yaml',
                'average_time_per_stock'
            ]
            
            for metric in metrics_to_track:
                values = []
                for snapshot in recent_history:
                    stats = snapshot.get('stats', {})
                    if metric in stats:
                        values.append(stats[metric])
                
                if len(values) >= 2:
                    # Simple linear trend calculation
                    n = len(values)
                    x_sum = sum(range(n))
                    y_sum = sum(values)
                    xy_sum = sum(i * values[i] for i in range(n))
                    x2_sum = sum(i * i for i in range(n))
                    
                    # Calculate slope (trend)
                    denominator = n * x2_sum - x_sum * x_sum
                    if denominator != 0:
                        slope = (n * xy_sum - x_sum * y_sum) / denominator
                        trends[f"{metric}_trend"] = slope
            
            return trends
            
        except Exception as e:
            logger.error(f"Error calculating performance trends: {e}")
            return {}
    
    def reset_stats(self):
        """Reset evolution statistics for a new run"""
        try:
            # Save current stats to history before reset
            if self.evolution_stats['start_time']:
                self.save_generation_snapshot()
            
            # Reset stats
            self.evolution_stats = {
                'stocks_tested': 0,
                'strategies_processed': 0,
                'variants_generated': 0,
                'variants_above_threshold': 0,
                'variants_added_to_yaml': 0,
                'optimization_tasks_completed': 0,
                'optimization_tasks_failed': 0,
                'start_time': None,
                'current_stock': None,
                'current_strategy': None,
                'generation_counter': self.evolution_stats['generation_counter'],  # Keep generation counter
                'total_processing_time': 0.0,
                'average_time_per_stock': 0.0,
                'gpu_utilization_time': 0.0,
                'cpu_fallback_time': 0.0
            }
            
            logger.info("📊 Evolution statistics reset for new cycle")
            
        except Exception as e:
            logger.error(f"Error resetting stats: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            report = {
                'current_stats': self.get_evolution_stats(),
                'success_rates': self.get_success_rates(),
                'trends': self.calculate_performance_trends(),
                'generation_history': self.get_generation_history(),
                'report_timestamp': datetime.now().isoformat()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {}
    
    def log_milestone(self, milestone: str, details: str = ""):
        """Log a significant milestone in the evolution process"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            milestone_msg = f"[{timestamp}] 🎯 MILESTONE: {milestone}"
            
            if details:
                milestone_msg += f" - {details}"
            
            logger.info(milestone_msg)
            logger.info(f"MILESTONE: {milestone} - {details}")
            
        except Exception as e:
            logger.error(f"Error logging milestone: {e}")
    
    def estimate_completion_time(self, total_stocks: int, total_strategies: int) -> Optional[datetime]:
        """Estimate completion time based on current progress"""
        try:
            if (self.evolution_stats['average_time_per_stock'] <= 0 or 
                self.evolution_stats['stocks_tested'] <= 0):
                return None
            
            remaining_stocks = total_stocks - self.evolution_stats['stocks_tested']
            remaining_strategies = total_strategies - self.evolution_stats['strategies_processed']
            
            if remaining_stocks <= 0 and remaining_strategies <= 0:
                return datetime.now()  # Already complete
            
            # Estimate based on average time per stock
            estimated_remaining_time = remaining_stocks * self.evolution_stats['average_time_per_stock']
            
            completion_time = datetime.now() + timedelta(seconds=estimated_remaining_time)
            return completion_time
            
        except Exception as e:
            logger.error(f"Error estimating completion time: {e}")
            return None
