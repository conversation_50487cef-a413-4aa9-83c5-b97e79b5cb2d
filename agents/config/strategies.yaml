evolution_config:
  base_templates:
  - Momentum_Scalping_Base
  - RSI_Intraday_Base
  - EMA_Crossover_Intraday_Base
  - EMA_9_Scalping_Base
  - Three_MA_Crossover_Scalping_Base
  - Range_Trading_Scalping_Base
  - Breakout_Scalping_Base
  - Volume_Scalping_Base
  - VWAP_Intraday_Base
  - MACD_Crossover_Intraday_Base
  - Bollinger_Bands_Intraday_Base
  - Stochastic_Intraday_Base
  - Parabolic_SAR_Intraday_Base
  - ADX_Intraday_Base
  - Fibonacci_Intraday_Base
  - Momentum_Intraday_Base
  - Price_Action_Intraday_Base
  - Gap_Trading_Intraday_Base
  - Reversal_Intraday_Base
  - Trend_Following_Intraday_Base
  max_strategies_per_stock_timeframe: 3
  min_performance_threshold: 0.05
  ranking_threshold: 80
  target_timeframes:
  - 1min
  - 3min
  - 5min
  - 15min
  target_stocks: [ALL_STOCKS]
strategies:
- description: Momentum Scalping Strategy
  entry_long: close > open & close > close.shift(1) & volume > volume.rolling(10).mean() * 1.2
  entry_short: close < open & close < close.shift(1) & volume > volume.rolling(10).mean() * 1.2
  exit_long: close < close.shift(1) | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > close.shift(1) | close > (Entry * (1 + stop_loss_pct))
  name: Momentum_Scalping_Base
  parameters:
    volume_multiplier: 1.5
    volume_period: 10
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: 9 EMA Scalping Strategy
  entry_long: close > ema_9
  entry_short: close < ema_9
  exit_long: close < ema_9 | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > ema_9 | close > (Entry * (1 + stop_loss_pct))
  name: EMA_9_Scalping_Base
  parameters:
    ema_period: 9
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Three Moving Average Crossover Scalping Strategy
  entry_long: ema_5 > ema_10 & ema_10 > ema_20
  entry_short: ema_5 < ema_10 & ema_10 < ema_20
  exit_long: ema_5 < ema_10 | close < (Entry * (1 - stop_loss_pct))
  exit_short: ema_5 > ema_10 | close > (Entry * (1 + stop_loss_pct))
  name: Three_MA_Crossover_Scalping_Base
  parameters:
    ema_fast: 5
    ema_medium: 10
    ema_slow: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Range Trading Scalping Strategy
  entry_long: close < bollinger_lower & close.shift(1) >= bollinger_lower.shift(1)
  entry_short: close > bollinger_upper & close.shift(1) <= bollinger_upper.shift(1)
  exit_long: close > bollinger_middle | close < (Entry * (1 - stop_loss_pct))
  exit_short: close < bollinger_middle | close > (Entry * (1 + stop_loss_pct))
  name: Range_Trading_Scalping_Base
  parameters:
    bollinger_period: 20
    bollinger_std_dev: 2
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Breakout Scalping Strategy
  entry_long: close > high.shift(1)
  entry_short: close < low.shift(1)
  exit_long: close < close.shift(1) | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > close.shift(1) | close > (Entry * (1 + stop_loss_pct))
  name: Breakout_Scalping_Base
  parameters: {}
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Volume Scalping Strategy
  entry_long: volume > volume.rolling(20).mean() * 1.5 & close > open
  entry_short: volume > volume.rolling(20).mean() * 1.5 & close < open
  exit_long: close < open | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > open | close > (Entry * (1 + stop_loss_pct))
  name: Volume_Scalping_Base
  parameters:
    volume_period: 20
    volume_multiplier: 2
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: MACD Crossover Scalping Strategy
  entry_long: macd_line > signal_line & macd_hist > 0
  entry_short: macd_line < signal_line & macd_hist < 0
  exit_long: macd_line < signal_line | close < (Entry * (1 - stop_loss_pct))
  exit_short: macd_line > signal_line | close > (Entry * (1 + stop_loss_pct))
  name: MACD_Crossover_Scalping_Base
  parameters:
    macd_fast: 12
    macd_slow: 26
    macd_signal: 9
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Bollinger Bands Scalping Strategy
  entry_long: close < bollinger_lower & close.shift(1) >= bollinger_lower.shift(1)
  entry_short: close > bollinger_upper & close.shift(1) <= bollinger_upper.shift(1)
  exit_long: close > bollinger_middle | close < (Entry * (1 - stop_loss_pct))
  exit_short: close < bollinger_middle | close > (Entry * (1 + stop_loss_pct))
  name: Bollinger_Bands_Scalping_Base
  parameters:
    bollinger_period: 20
    bollinger_std_dev: 2
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: VWAP Scalping Strategy
  entry_long: close > vwap & close.shift(1) <= vwap.shift(1)
  entry_short: close < vwap & close.shift(1) >= vwap.shift(1)
  exit_long: close < vwap | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > vwap | close > (Entry * (1 + stop_loss_pct))
  name: VWAP_Scalping_Base
  parameters: {}
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Parabolic SAR Scalping Strategy
  entry_long: sar_indicator < close & sar_indicator.shift(1) >= close.shift(1)
  entry_short: sar_indicator > close & sar_indicator.shift(1) <= close.shift(1)
  exit_long: sar_indicator > close | close < (Entry * (1 - stop_loss_pct))
  exit_short: sar_indicator < close | close > (Entry * (1 + stop_loss_pct))
  name: Parabolic_SAR_Scalping_Base
  parameters:
    acceleration_factor: 0.02
    max_acceleration_factor: 0.2
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Stochastic Oscillator Scalping Strategy
  entry_long: stochastic_k < 20 & stochastic_d < 20 & stochastic_k > stochastic_d
  entry_short: stochastic_k > 80 & stochastic_d > 80 & stochastic_k < stochastic_d
  exit_long: stochastic_k > 50 | close < (Entry * (1 - stop_loss_pct))
  exit_short: stochastic_k < 50 | close > (Entry * (1 + stop_loss_pct))
  name: Stochastic_Scalping_Base
  parameters:
    stochastic_k_period: 14
    stochastic_d_period: 3
    stochastic_smoothing: 3
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Schaff Trend Cycle Scalping Strategy
  entry_long: stc_line < 25 & stc_line.shift(1) >= 25
  entry_short: stc_line > 75 & stc_line.shift(1) <= 75
  exit_long: stc_line > 50 | close < (Entry * (1 - stop_loss_pct))
  exit_short: stc_line < 50 | close > (Entry * (1 + stop_loss_pct))
  name: STC_Scalping_Base
  parameters:
    stc_fast: 23
    stc_slow: 50
    stc_cycle: 10
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: ADX Trend Scalping Strategy
  entry_long: adx_14 > 20 & plusdi_14 > minusdi_14
  entry_short: adx_14 > 20 & plusdi_14 < minusdi_14
  exit_long: plusdi_14 < minusdi_14 | close < (Entry * (1 - stop_loss_pct))
  exit_short: plusdi_14 > minusdi_14 | close > (Entry * (1 + stop_loss_pct))
  name: ADX_Scalping_Base
  parameters:
    adx_period: 14
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Fibonacci Retracement Scalping Strategy
  entry_long: close > fibonacci_38_2 & close.shift(1) <= fibonacci_38_2.shift(1)
  entry_short: close < fibonacci_61_8 & close.shift(1) >= fibonacci_61_8.shift(1)
  exit_long: close < fibonacci_23_6 | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > fibonacci_78_6 | close > (Entry * (1 + stop_loss_pct))
  name: Fibonacci_Scalping_Base
  parameters:
    fib_high_period: 20
    fib_low_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.02
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: RSI Overbought/Oversold Intraday Strategy
  entry_long: rsi_14 < 30 & volume > volume.rolling(20).mean()
  entry_short: rsi_14 > 70 & volume > volume.rolling(20).mean()
  exit_long: rsi_14 > 50 | close < (Entry * (1 - stop_loss_pct))
  exit_short: rsi_14 < 50 | close > (Entry * (1 + stop_loss_pct))
  name: RSI_Intraday_Base
  parameters:
    rsi_period: 14
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: EMA Crossover Intraday Strategy
  entry_long: ema_5 > ema_20 & volume > volume.rolling(20).mean()
  entry_short: ema_5 < ema_20 & volume > volume.rolling(20).mean()
  exit_long: ema_5 < ema_20 | close < (Entry * (1 - stop_loss_pct))
  exit_short: ema_5 > ema_20 | close > (Entry * (1 + stop_loss_pct))
  name: EMA_Crossover_Intraday_Base
  parameters:
    ema_fast: 5
    ema_slow: 20
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: VWAP Intraday Strategy
  entry_long: close > vwap & volume > volume.rolling(20).mean()
  entry_short: close < vwap & volume > volume.rolling(20).mean()
  exit_long: close < vwap | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > vwap | close > (Entry * (1 + stop_loss_pct))
  name: VWAP_Intraday_Base
  parameters:
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: MACD Crossover Intraday Strategy
  entry_long: macd_line > signal_line & macd_hist > 0 & volume > volume.rolling(20).mean()
  entry_short: macd_line < signal_line & macd_hist < 0 & volume > volume.rolling(20).mean()
  exit_long: macd_line < signal_line | close < (Entry * (1 - stop_loss_pct))
  exit_short: macd_line > signal_line | close > (Entry * (1 + stop_loss_pct))
  name: MACD_Crossover_Intraday_Base
  parameters:
    macd_fast: 12
    macd_slow: 26
    macd_signal: 9
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Bollinger Bands Intraday Strategy
  entry_long: close < bollinger_lower & close.shift(1) >= bollinger_lower.shift(1) & volume > volume.rolling(20).mean()
  entry_short: close > bollinger_upper & close.shift(1) <= bollinger_upper.shift(1) & volume > volume.rolling(20).mean()
  exit_long: close > bollinger_middle | close < (Entry * (1 - stop_loss_pct))
  exit_short: close < bollinger_middle | close > (Entry * (1 + stop_loss_pct))
  name: Bollinger_Bands_Intraday_Base
  parameters:
    bollinger_period: 20
    bollinger_std_dev: 2
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Stochastic Oscillator Intraday Strategy
  entry_long: stochastic_k < 20 & stochastic_d < 20 & stochastic_k > stochastic_d & volume > volume.rolling(20).mean()
  entry_short: stochastic_k > 80 & stochastic_d > 80 & stochastic_k < stochastic_d & volume > volume.rolling(20).mean()
  exit_long: stochastic_k > 50 | close < (Entry * (1 - stop_loss_pct))
  exit_short: stochastic_k < 50 | close > (Entry * (1 + stop_loss_pct))
  name: Stochastic_Intraday_Base
  parameters:
    stochastic_k_period: 14
    stochastic_d_period: 3
    stochastic_smoothing: 3
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Parabolic SAR Intraday Strategy
  entry_long: sar_indicator < close & sar_indicator.shift(1) >= close.shift(1) & volume > volume.rolling(20).mean()
  entry_short: sar_indicator > close & sar_indicator.shift(1) <= close.shift(1) & volume > volume.rolling(20).mean()
  exit_long: sar_indicator > close | close < (Entry * (1 - stop_loss_pct))
  exit_short: sar_indicator < close | close > (Entry * (1 + stop_loss_pct))
  name: Parabolic_SAR_Intraday_Base
  parameters:
    acceleration_factor: 0.02
    max_acceleration_factor: 0.2
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: ADX Trend Intraday Strategy
  entry_long: adx_14 > 20 & plusdi_14 > minusdi_14 & volume > volume.rolling(20).mean()
  entry_short: (adx_14 > 20) & (plusdi_14 < minusdi_14) & (volume > volume.rolling(20).mean())
  exit_long: plusdi_14 < minusdi_14 | close < (Entry * (1 - stop_loss_pct))
  exit_short: plusdi_14 > minusdi_14 | close > (Entry * (1 + stop_loss_pct))
  name: ADX_Intraday_Base
  parameters:
    adx_period: 14
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Fibonacci Retracement Intraday Strategy
  entry_long: (close > fibonacci_38_2) & (close.shift(1) <= fibonacci_38_2.shift(1)) & (volume > volume.rolling(20).mean())
  entry_short: (close < fibonacci_61_8) & (close.shift(1) >= fibonacci_61_8.shift(1)) & (volume > volume.rolling(20).mean())
  exit_long: close < fibonacci_23_6 | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > fibonacci_78_6 | close > (Entry * (1 + stop_loss_pct))
  name: Fibonacci_Intraday_Base
  parameters:
    fib_high_period: 20
    fib_low_period: 20
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Momentum Intraday Strategy
  entry_long: (close > open) & (close > close.shift(1)) & (volume > volume.rolling(10).mean() * 1.2)
  entry_short: (close < open) & (close < close.shift(1)) & (volume > volume.rolling(10).mean() * 1.2)
  exit_long: close < close.shift(1) | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > close.shift(1) | close > (Entry * (1 + stop_loss_pct))
  name: Momentum_Intraday_Base
  parameters:
    volume_multiplier: 1.5
    volume_period: 10
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Price Action Intraday Strategy
  entry_long: (close > open) & (high - close < close - low)
  entry_short: (close < open) & (close - low < high - close)
  exit_long: close < open | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > open | close > (Entry * (1 + stop_loss_pct))
  name: Price_Action_Intraday_Base
  parameters: {}
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Gap Trading Intraday Strategy
  entry_long: (open < close.shift(1) * 0.99) & (close > open)
  entry_short: (open > close.shift(1) * 1.01) & (close < open)
  exit_long: close > open.shift(1) | close < (Entry * (1 - stop_loss_pct))
  exit_short: close < open.shift(1) | close > (Entry * (1 + stop_loss_pct))
  name: Gap_Trading_Intraday_Base
  parameters: {}
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Reversal Intraday Strategy
  entry_long: (close < low.shift(1)) & (close > open)
  entry_short: (close > high.shift(1)) & (close < open)
  exit_long: close > high.shift(1) | close < (Entry * (1 - stop_loss_pct))
  exit_short: close < low.shift(1) | close > (Entry * (1 + stop_loss_pct))
  name: Reversal_Intraday_Base
  parameters: {}
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template

- description: Trend Following Intraday Strategy
  entry_long: (close > ema_50) & (close.shift(1) < ema_50.shift(1))
  entry_short: (close < ema_50) & (close.shift(1) > ema_50.shift(1))
  exit_long: close < ema_50 | close < (Entry * (1 - stop_loss_pct))
  exit_short: close > ema_50 | close > (Entry * (1 + stop_loss_pct))
  name: Trend_Following_Intraday_Base
  parameters:
    ema_period: 50
  position_sizing:
    method: fixed_percentage
    percentage: 0.03
  ranking: 0
  risk_reward_ratios:
  - - 1
    - 2
  - - 1
    - 3
  type: base_template
