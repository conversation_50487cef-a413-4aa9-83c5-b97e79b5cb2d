#!/usr/bin/env python3
"""
Live Trading Orchestrator - Multi-Agent Trading System Coordinator

This module implements the live trading orchestrator that coordinates all agents
in the multi-agent trading system as described in the system architecture.

Features:
🏭 Multi-Agent Coordination
- Shared service layer (single instance each)
- Dynamic worker layer (execution workers)
- Inter-agent communication and coordination

⚙️ System Architecture Implementation
- Market Monitoring Agent (monitors ALL symbols)
- Signal Generator Agent (analyzes all market data)
- Risk Management Agent (calculates position parameters)
- Performance Analysis Agent (tracks and optimizes system performance)
- Execution Worker Pool (dynamic creation based on MAX_TRADES)

🔄 State Management & Recovery
- System state persistence and recovery
- Position reconciliation with broker
- Graceful shutdown and restart handling
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from pathlib import Path
import json
import yaml

from .execution.worker_state_manager import WorkerStateManager
from .execution.execution_worker_pool import ExecutionWorkerPool
from .market_monitoring.data_structures import OHLCV
try:
    from .market_monitoring.market_monitoring_agent import MarketMonitoringAgent
except ImportError:
    # Fallback to available market monitoring agent
    try:
        from .market_monitoring.market_data_agent import MarketDataAgent as MarketMonitoringAgent
    except ImportError:
        from .market_monitoring.modern_market_data_agent import ModernMarketDataAgent as MarketMonitoringAgent
from .signal_generation.signal_generation_agent import SignalGenerationAgent
from .risk_management.risk_management_agent import RiskManagementAgent, RiskAssessment
from .performance_analysis.performance_analysis_agent import PerformanceAnalysisAgent
from .agent_communication_interface import AgentCommunicationInterface, MessageType

logger = logging.getLogger(__name__)


@dataclass
class SystemConfiguration:
    """System configuration for live trading"""
    max_trades: int
    trading_mode: str
    symbol_universe: List[str]
    market_hours_start: str = "09:15"
    market_hours_end: str = "15:30"
    enable_pre_market: bool = False
    enable_after_hours: bool = False
    
    # Agent configurations
    market_monitoring_config: str = "agents/config/market_monitoring_config.yaml"
    signal_generation_config: str = "agents/config/signal_generation_config.yaml"
    risk_management_config: str = "agents/config/risk_management_config.yaml"
    performance_analysis_config: str = "agents/config/performance_analysis_config.yaml"
    execution_config: str = "agents/config/execution_config.yaml"
    risk_model_path: Optional[str] = None # Path to the trained RRR selection model


class LiveTradingOrchestrator:
    """
    Orchestrates the multi-agent trading system for live trading
    Implements the architecture described in the system requirements
    """
    
    def __init__(self, config: SystemConfiguration = None):
        """Initialize Live Trading Orchestrator"""
        self.config = config or self._load_default_config()
        
        # ═══════════════════════════════════════════════════════════════════════════════
        # SHARED SERVICE LAYER (Single Instance Each)
        # ═══════════════════════════════════════════════════════════════════════════════
        self.market_monitoring_agent: Optional[MarketMonitoringAgent] = None
        self.signal_generation_agent: Optional[SignalGenerationAgent] = None
        self.risk_management_agent: Optional[RiskManagementAgent] = None
        self.performance_analysis_agent: Optional[PerformanceAnalysisAgent] = None
        
        # ═══════════════════════════════════════════════════════════════════════════════
        # WORKER LAYER (Dynamic Creation)
        # ═══════════════════════════════════════════════════════════════════════════════
        self.worker_state_manager: Optional[WorkerStateManager] = None
        self.execution_worker_pool: Optional[ExecutionWorkerPool] = None
        
        # ═══════════════════════════════════════════════════════════════════════════════
        # COMMUNICATION & COORDINATION
        # ═══════════════════════════════════════════════════════════════════════════════
        self.communication_interface: Optional[AgentCommunicationInterface] = None
        
        # System state
        self.is_running = False
        self.is_market_hours = False
        self.system_start_time: Optional[datetime] = None
        self.active_signals: Dict[str, Any] = {}
        self.system_metrics: Dict[str, Any] = {}
        
        # Background tasks
        self.market_hours_monitor_task: Optional[asyncio.Task] = None
        self.system_health_monitor_task: Optional[asyncio.Task] = None
        self.performance_monitor_task: Optional[asyncio.Task] = None
        
        logger.info(f"[INIT] Live Trading Orchestrator initialized - MAX_TRADES: {self.config.max_trades}")
    
    def _load_default_config(self) -> SystemConfiguration:
        """Load default system configuration"""
        return SystemConfiguration(
            max_trades=int(os.getenv('MAX_TRADES', '5')),
            trading_mode=os.getenv('TRADING_MODE', 'paper'),
            symbol_universe=self._get_default_symbol_universe()
        )
    
    def _get_default_symbol_universe(self) -> List[str]:
        """Get default symbol universe"""
        return [
            'RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ', 'INFY-EQ', 'HINDUNILVR-EQ',
            'ICICIBANK-EQ', 'KOTAKBANK-EQ', 'SBIN-EQ', 'BHARTIARTL-EQ', 'ITC-EQ',
            'ASIANPAINT-EQ', 'LT-EQ', 'AXISBANK-EQ', 'MARUTI-EQ', 'SUNPHARMA-EQ',
            'TITAN-EQ', 'ULTRACEMCO-EQ', 'WIPRO-EQ', 'NESTLEIND-EQ', 'POWERGRID-EQ'
        ]
    
    async def initialize(self) -> bool:
        """Initialize the complete multi-agent trading system"""
        try:
            logger.info("[INIT] Initializing Live Trading Orchestrator...")
            self.system_start_time = datetime.now()
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # STEP 1: INITIALIZE COMMUNICATION INTERFACE
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[INIT] Step 1: Initializing communication interface...")
            self.communication_interface = AgentCommunicationInterface("live_trading_orchestrator")
            await self.communication_interface.initialize()
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # STEP 2: INITIALIZE WORKER STATE MANAGEMENT
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[INIT] Step 2: Initializing worker state management...")
            self.worker_state_manager = WorkerStateManager()
            if not await self.worker_state_manager.initialize(self.config.symbol_universe):
                logger.error("[ERROR] Failed to initialize worker state manager")
                return False
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # STEP 3: INITIALIZE SHARED SERVICE LAYER
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[INIT] Step 3: Initializing shared service layer...")
            
            # Market Monitoring Agent (monitors ALL symbols)
            logger.info("[INIT] Step 3a: Initializing Market Monitoring Agent...")
            market_monitoring_config_path = self.config.market_monitoring_config
            if Path(market_monitoring_config_path).exists():
                with open(market_monitoring_config_path, 'r') as f:
                    market_monitoring_config_data = yaml.safe_load(f)
                self.market_monitoring_agent = MarketMonitoringAgent(market_monitoring_config_data)
                await self.market_monitoring_agent.initialize()
            else:
                logger.error(f"[ERROR] Market Monitoring config file not found: {market_monitoring_config_path}")
                return False
            
            # Signal Generation Agent (analyzes all market data)
            logger.info("[INIT] Step 3b: Initializing Signal Generation Agent...")
            self.signal_generation_agent = SignalGenerationAgent(self.config.signal_generation_config)
            await self.signal_generation_agent.setup()
            
            # Risk Management Agent (calculates position parameters)
            logger.info("[INIT] Step 3c: Initializing Risk Management Agent...")
            # Create a dummy event bus and session ID for compatibility
            from utils.event_bus import EventBus
            import uuid

            risk_event_bus = EventBus()
            risk_session_id = str(uuid.uuid4())

            self.risk_management_agent = RiskManagementAgent(
                event_bus=risk_event_bus,
                config=self.config,
                session_id=risk_session_id,
                risk_model_path=self.config.risk_model_path
            )
            await self.risk_management_agent.initialize()
            
            # Performance Analysis Agent (tracks and optimizes system performance)
            logger.info("[INIT] Step 3d: Initializing Performance Analysis Agent...")
            self.performance_analysis_agent = PerformanceAnalysisAgent(self.config.performance_analysis_config)
            await self.performance_analysis_agent.setup()
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # STEP 4: INITIALIZE EXECUTION WORKER POOL
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[INIT] Step 4: Initializing execution worker pool...")
            self.execution_worker_pool = ExecutionWorkerPool({
                'trading_mode': self.config.trading_mode,
                'trading_config': {'max_trades': self.config.max_trades}
            })
            
            if not await self.execution_worker_pool.initialize(self.config.symbol_universe):
                logger.error("[ERROR] Failed to initialize execution worker pool")
                return False
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # STEP 5: SETUP INTER-AGENT COMMUNICATION
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[INIT] Step 5: Setting up inter-agent communication...")
            await self._setup_agent_communication()
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # STEP 6: SETUP EVENT HANDLERS AND CALLBACKS
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[INIT] Step 6: Setting up event handlers...")
            await self._setup_event_handlers()
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # STEP 7: SYSTEM STATE RECOVERY (if needed)
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[INIT] Step 7: Performing system state recovery...")
            await self._perform_system_recovery()
            
            logger.info("[SUCCESS] Live Trading Orchestrator initialization complete")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Live Trading Orchestrator: {e}")
            await self._cleanup_on_failure()
            return False
    
    async def start_live_trading(self) -> bool:
        """Start the live trading system"""
        try:
            logger.info("[START] Starting live trading system...")
            
            if not self.worker_state_manager or not self.execution_worker_pool:
                logger.error("[ERROR] System not properly initialized")
                return False
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # START ALL SHARED SERVICES
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[START] Starting shared service layer...")
            
            # Start market monitoring (monitors ALL symbols)
            if self.market_monitoring_agent:
                await self.market_monitoring_agent.start()
                logger.info("[START] Market Monitoring Agent started")
            
            # Start signal generation
            if self.signal_generation_agent:
                await self.signal_generation_agent.start()
                logger.info("[START] Signal Generation Agent started")
            
            # Start risk management
            if self.risk_management_agent:
                await self.risk_management_agent.start()
                logger.info("[START] Risk Management Agent started")
            
            # Start performance analysis
            if self.performance_analysis_agent:
                await self.performance_analysis_agent.start()
                logger.info("[START] Performance Analysis Agent started")
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # START BACKGROUND MONITORING TASKS
            # ═══════════════════════════════════════════════════════════════════════════════
            logger.info("[START] Starting background monitoring tasks...")
            await self._start_background_tasks()
            
            # ═══════════════════════════════════════════════════════════════════════════════
            # SYSTEM IS NOW RUNNING
            # ═══════════════════════════════════════════════════════════════════════════════
            self.is_running = True
            logger.info("[SUCCESS] Live trading system started successfully")
            
            # Log system status
            await self._log_system_status()
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start live trading system: {e}")
            return False
    
    async def shutdown(self):
        """Gracefully shutdown the live trading system"""
        try:
            logger.info("[SHUTDOWN] Initiating graceful shutdown...")
            self.is_running = False
            
            # Cancel background tasks
            tasks_to_cancel = [
                self.market_hours_monitor_task,
                self.system_health_monitor_task,
                self.performance_monitor_task
            ]
            
            for task in tasks_to_cancel:
                if task and not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            # Shutdown execution worker pool first (handles active trades)
            if self.execution_worker_pool:
                await self.execution_worker_pool.shutdown()
                logger.info("[SHUTDOWN] Execution worker pool shutdown complete")
            
            # Shutdown shared services
            if self.performance_analysis_agent:
                if hasattr(self.performance_analysis_agent, 'shutdown'):
                    await self.performance_analysis_agent.shutdown()
                elif hasattr(self.performance_analysis_agent, 'stop'):
                    await self.performance_analysis_agent.stop()
                logger.info("[SHUTDOWN] Performance Analysis Agent shutdown")
            
            if self.risk_management_agent:
                await self.risk_management_agent.shutdown()
                logger.info("[SHUTDOWN] Risk Management Agent shutdown")
            
            if self.signal_generation_agent:
                if hasattr(self.signal_generation_agent, 'shutdown'):
                    await self.signal_generation_agent.shutdown()
                elif hasattr(self.signal_generation_agent, 'stop'):
                    await self.signal_generation_agent.stop()
                logger.info("[SHUTDOWN] Signal Generation Agent shutdown")
            
            if self.market_monitoring_agent:
                await self.market_monitoring_agent.shutdown()
                logger.info("[SHUTDOWN] Market Monitoring Agent shutdown")
            
            # Shutdown worker state manager (final state backup)
            if self.worker_state_manager:
                await self.worker_state_manager.shutdown()
                logger.info("[SHUTDOWN] Worker State Manager shutdown")
            
            # Shutdown communication interface
            if self.communication_interface:
                await self.communication_interface.shutdown()
                logger.info("[SHUTDOWN] Communication interface shutdown")
            
            logger.info("[SUCCESS] Live trading system shutdown complete")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during shutdown: {e}")
    
    async def _setup_agent_communication(self):
        """Setup inter-agent communication channels"""
        try:
            logger.info("[COMM] Setting up inter-agent communication...")
            
            # Register all agents with communication interface
            agents = {
                'market_monitoring': self.market_monitoring_agent,
                'signal_generation': self.signal_generation_agent,
                'risk_management': self.risk_management_agent,
                'performance_analysis': self.performance_analysis_agent,
                'execution_worker_pool': self.execution_worker_pool
            }
            
            for agent_name, agent in agents.items():
                if agent and hasattr(agent, 'register_communication_interface'):
                    await agent.register_communication_interface(self.communication_interface)
                    logger.info(f"[COMM] Registered {agent_name} with communication interface")
            
            logger.info("[SUCCESS] Inter-agent communication setup complete")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to setup agent communication: {e}")
            raise

    async def _setup_event_handlers(self):
        """Setup event handlers and callbacks between agents"""
        try:
            logger.info("[EVENTS] Setting up event handlers...")

            # ═══════════════════════════════════════════════════════════════════════════════
            # SIGNAL FLOW: Market Data → Signal Generation → Risk Assessment → Execution
            # ═══════════════════════════════════════════════════════════════════════════════

            # Market Monitoring → Signal Generation
            if self.market_monitoring_agent and self.signal_generation_agent:
                self.market_monitoring_agent.add_signal_handler(self._handle_market_data_signal)

            # Signal Generation → Risk Management → Execution
            if self.signal_generation_agent:
                self.signal_generation_agent.add_signal_handler(self._handle_trading_signal)

            # Execution Worker Pool callbacks
            if self.execution_worker_pool:
                self.execution_worker_pool.set_callbacks(
                    on_trade_completed=self._handle_trade_completed,
                    on_trade_failed=self._handle_trade_failed,
                    on_worker_error=self._handle_worker_error
                )

            logger.info("[SUCCESS] Event handlers setup complete")

        except Exception as e:
            logger.error(f"[ERROR] Failed to setup event handlers: {e}")
            raise

    async def _perform_system_recovery(self):
        """Perform system state recovery on startup"""
        try:
            logger.info("[RECOVERY] Performing system state recovery...")

            # Check if this is a system restart with active positions
            if self.worker_state_manager:
                system_status = self.worker_state_manager.get_system_status()
                active_trades = system_status.get('worker_states', {}).get('ACTIVE', 0)

                if active_trades > 0:
                    logger.warning(f"[RECOVERY] Found {active_trades} active trades from previous session")

                    # TODO: Implement broker position reconciliation
                    # This would query the broker API to get actual positions
                    # and reconcile with the system state

                    logger.info("[RECOVERY] Position reconciliation would be performed here")

                # Check if it's a new trading day
                today = datetime.now().strftime('%Y-%m-%d')
                last_backup = system_status.get('last_backup')

                if last_backup:
                    last_backup_date = datetime.fromisoformat(last_backup).strftime('%Y-%m-%d')
                    if last_backup_date != today:
                        logger.info("[RECOVERY] New trading day detected - resetting daily counters")
                        # Daily reset is handled automatically by WorkerStateManager

            logger.info("[SUCCESS] System recovery complete")

        except Exception as e:
            logger.error(f"[ERROR] System recovery failed: {e}")
            raise

    async def _start_background_tasks(self):
        """Start background monitoring tasks"""
        try:
            logger.info("[TASKS] Starting background monitoring tasks...")

            # Market hours monitoring
            self.market_hours_monitor_task = asyncio.create_task(self._market_hours_monitor_loop())

            # System health monitoring
            self.system_health_monitor_task = asyncio.create_task(self._system_health_monitor_loop())

            # Performance monitoring
            self.performance_monitor_task = asyncio.create_task(self._performance_monitor_loop())

            logger.info("[SUCCESS] Background tasks started")

        except Exception as e:
            logger.error(f"[ERROR] Failed to start background tasks: {e}")
            raise

    async def _market_hours_monitor_loop(self):
        """Monitor market hours and adjust system behavior"""
        while self.is_running:
            try:
                now = datetime.now()
                current_time = now.strftime('%H:%M')

                # Check if we're in market hours
                market_start = self.config.market_hours_start
                market_end = self.config.market_hours_end

                is_market_hours = market_start <= current_time <= market_end

                # Check if market status changed
                if is_market_hours != self.is_market_hours:
                    self.is_market_hours = is_market_hours

                    if is_market_hours:
                        logger.info("[MARKET] Market hours started - system active")
                        await self._handle_market_open()
                    else:
                        logger.info("[MARKET] Market hours ended - system in after-hours mode")
                        await self._handle_market_close()

                # Sleep for 1 minute before next check
                await asyncio.sleep(60)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Market hours monitor error: {e}")
                await asyncio.sleep(60)

    async def _system_health_monitor_loop(self):
        """Monitor system health and performance"""
        while self.is_running:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes

                # Check system health
                await self._check_system_health()

                # Update system metrics
                await self._update_system_metrics()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] System health monitor error: {e}")
                await asyncio.sleep(300)

    async def _performance_monitor_loop(self):
        """Monitor and optimize system performance"""
        while self.is_running:
            try:
                await asyncio.sleep(900)  # Check every 15 minutes

                # Analyze performance and make adjustments
                if self.performance_analysis_agent:
                    await self._analyze_and_optimize_performance()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Performance monitor error: {e}")
                await asyncio.sleep(900)

    # ═══════════════════════════════════════════════════════════════════════════════
    # EVENT HANDLERS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _handle_market_data_signal(self, market_data: Dict[str, Any]):
        """Handle market data from market monitoring agent"""
        try:
            # Forward market data to signal generation agent
            if self.signal_generation_agent:
                await self.signal_generation_agent.process_market_data(
                    symbol=market_data.get('symbol'),
                    ohlcv_data=market_data.get('ohlcv_data'),
                    indicators=market_data.get('indicators'),
                    market_regime=market_data.get('market_regime')
                )

        except Exception as e:
            logger.error(f"[ERROR] Error handling market data signal: {e}")

    async def _handle_trading_signal(self, trading_signal: Any):
        """Handle trading signal from signal generation agent"""
        try:
            logger.info(f"[SIGNAL] Processing trading signal for {trading_signal.symbol}")

            # Check if we can accept new trades
            if not self.worker_state_manager.can_accept_new_trade(trading_signal.symbol):
                logger.info(f"[SIGNAL] Cannot accept new trade for {trading_signal.symbol} - no available worker")
                return

            # Risk assessment
            if self.risk_management_agent:
                risk_assessment = await self.risk_management_agent.assess_signal_risk(trading_signal)

                if not risk_assessment.approved:
                    logger.warning(f"[RISK] Signal rejected: {risk_assessment.rejection_reason}")
                    return

                # Execute trade through worker pool
                if self.execution_worker_pool:
                    execution_result = await self.execution_worker_pool.execute_trade(
                        trading_signal, risk_assessment
                    )

                    if execution_result.get('success'):
                        logger.info(f"[EXECUTE] Trade executed successfully: {execution_result}")
                        self.active_signals[trading_signal.symbol] = {
                            'signal': trading_signal,
                            'risk_assessment': risk_assessment,
                            'execution_result': execution_result,
                            'timestamp': datetime.now()
                        }
                    else:
                        logger.error(f"[EXECUTE] Trade execution failed: {execution_result}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling trading signal: {e}")

    async def _handle_trade_completed(self, worker_id: str, trade_id: str, pnl: float):
        """Handle trade completion"""
        try:
            logger.info(f"[TRADE] Trade completed: {trade_id} on worker {worker_id}, PnL: ₹{pnl:.2f}")

            # Update performance metrics
            if self.performance_analysis_agent:
                await self.performance_analysis_agent.record_trade_completion(
                    worker_id=worker_id,
                    trade_id=trade_id,
                    pnl=pnl,
                    timestamp=datetime.now()
                )

            # Remove from active signals
            for symbol, signal_data in list(self.active_signals.items()):
                if signal_data.get('execution_result', {}).get('trade_id') == trade_id:
                    del self.active_signals[symbol]
                    break

        except Exception as e:
            logger.error(f"[ERROR] Error handling trade completion: {e}")

    async def _handle_trade_failed(self, worker_id: str, trade_id: str, reason: str):
        """Handle trade failure"""
        try:
            logger.error(f"[TRADE] Trade failed: {trade_id} on worker {worker_id}, Reason: {reason}")

            # Record failure in performance analysis
            if self.performance_analysis_agent:
                await self.performance_analysis_agent.record_trade_failure(
                    worker_id=worker_id,
                    trade_id=trade_id,
                    reason=reason,
                    timestamp=datetime.now()
                )

        except Exception as e:
            logger.error(f"[ERROR] Error handling trade failure: {e}")

    async def _handle_worker_error(self, worker_id: str, error_message: str):
        """Handle worker error"""
        try:
            logger.error(f"[WORKER] Worker error: {worker_id} - {error_message}")

            # Notify risk management of worker issue
            if self.risk_management_agent:
                await self.risk_management_agent.handle_worker_error(worker_id, error_message)

        except Exception as e:
            logger.error(f"[ERROR] Error handling worker error: {e}")

    async def _handle_market_open(self):
        """Handle market opening"""
        try:
            logger.info("[MARKET] Handling market open...")

            # Notify all agents of market open
            if self.communication_interface:
                await self.communication_interface.broadcast_message(
                    MessageType.SYSTEM_EVENT,
                    {"event": "market_open", "timestamp": datetime.now().isoformat()}
                )

        except Exception as e:
            logger.error(f"[ERROR] Error handling market open: {e}")

    async def _handle_market_close(self):
        """Handle market closing"""
        try:
            logger.info("[MARKET] Handling market close...")

            # Close any remaining positions if configured to do so
            # This would be configurable behavior

            # Notify all agents of market close
            if self.communication_interface:
                await self.communication_interface.broadcast_message(
                    MessageType.SYSTEM_EVENT,
                    {"event": "market_close", "timestamp": datetime.now().isoformat()}
                )

        except Exception as e:
            logger.error(f"[ERROR] Error handling market close: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # SYSTEM MONITORING AND MAINTENANCE
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _check_system_health(self):
        """Check overall system health"""
        try:
            health_issues = []

            # Check worker state manager
            if self.worker_state_manager:
                worker_status = self.worker_state_manager.get_system_status()
                if worker_status.get('status') != 'running':
                    health_issues.append(f"Worker state manager: {worker_status.get('status')}")

            # Check execution worker pool
            if self.execution_worker_pool:
                pool_status = self.execution_worker_pool.get_pool_status()
                if not pool_status.get('pool_running'):
                    health_issues.append("Execution worker pool not running")

            # Check shared services
            services = {
                'market_monitoring': self.market_monitoring_agent,
                'signal_generation': self.signal_generation_agent,
                'risk_management': self.risk_management_agent,
                'performance_analysis': self.performance_analysis_agent
            }

            for service_name, service in services.items():
                if service and hasattr(service, 'is_healthy'):
                    if not await service.is_healthy():
                        health_issues.append(f"{service_name} service unhealthy")

            if health_issues:
                logger.warning(f"[HEALTH] System health issues detected: {health_issues}")
            else:
                logger.debug("[HEALTH] System health check passed")

        except Exception as e:
            logger.error(f"[ERROR] System health check failed: {e}")

    async def _update_system_metrics(self):
        """Update system-wide metrics"""
        try:
            if not self.worker_state_manager:
                return

            # Get current system status
            system_status = self.worker_state_manager.get_system_status()

            # Calculate uptime
            uptime_hours = (datetime.now() - self.system_start_time).total_seconds() / 3600

            # Update metrics
            self.system_metrics = {
                'uptime_hours': uptime_hours,
                'total_workers': system_status.get('total_workers', 0),
                'active_workers': system_status.get('worker_states', {}).get('ACTIVE', 0),
                'idle_workers': system_status.get('worker_states', {}).get('IDLE', 0),
                'cooldown_workers': system_status.get('worker_states', {}).get('COOLDOWN', 0),
                'total_trades_today': system_status.get('system_metrics', {}).get('total_trades_today', 0),
                'daily_pnl': system_status.get('system_metrics', {}).get('daily_pnl', 0.0),
                'active_signals_count': len(self.active_signals),
                'is_market_hours': self.is_market_hours,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"[ERROR] Error updating system metrics: {e}")

    async def _analyze_and_optimize_performance(self):
        """Analyze system performance and make optimizations"""
        try:
            logger.info("[OPTIMIZE] Analyzing system performance...")

            if not self.performance_analysis_agent:
                return

            # Get performance analysis
            performance_data = await self.performance_analysis_agent.get_system_performance()

            # Check if rebalancing is needed
            enable_rebalancing = os.getenv('ENABLE_STRATEGY_REALLOCATION', 'true').lower() == 'true'

            if enable_rebalancing and performance_data:
                # Analyze worker performance
                worker_performance = performance_data.get('worker_performance', {})

                # Check if any workers are significantly underperforming
                avg_success_rate = sum(w.get('success_rate', 0) for w in worker_performance.values()) / len(worker_performance) if worker_performance else 0

                underperforming_workers = [
                    worker_id for worker_id, perf in worker_performance.items()
                    if perf.get('success_rate', 0) < avg_success_rate * 0.7  # 30% below average
                ]

                if underperforming_workers:
                    logger.info(f"[OPTIMIZE] Found underperforming workers: {underperforming_workers}")

                    # Trigger rebalancing
                    if self.execution_worker_pool:
                        await self.execution_worker_pool.rebalance_workers()

        except Exception as e:
            logger.error(f"[ERROR] Performance analysis and optimization failed: {e}")

    async def _log_system_status(self):
        """Log current system status"""
        try:
            if self.worker_state_manager:
                system_status = self.worker_state_manager.get_system_status()

                logger.info("[STATUS] ═══════════════════════════════════════════════════════════════")
                logger.info(f"[STATUS] Live Trading System Status")
                logger.info("[STATUS] ═══════════════════════════════════════════════════════════════")
                logger.info(f"[STATUS] System Running: {self.is_running}")
                logger.info(f"[STATUS] Market Hours: {self.is_market_hours}")
                logger.info(f"[STATUS] Max Trades: {system_status.get('max_trades', 0)}")
                logger.info(f"[STATUS] Total Workers: {system_status.get('total_workers', 0)}")
                logger.info(f"[STATUS] Active Workers: {system_status.get('worker_states', {}).get('ACTIVE', 0)}")
                logger.info(f"[STATUS] Idle Workers: {system_status.get('worker_states', {}).get('IDLE', 0)}")
                logger.info(f"[STATUS] Cooldown Workers: {system_status.get('worker_states', {}).get('COOLDOWN', 0)}")
                logger.info(f"[STATUS] Trades Today: {system_status.get('system_metrics', {}).get('total_trades_today', 0)}")
                logger.info(f"[STATUS] Daily PnL: ₹{system_status.get('system_metrics', {}).get('daily_pnl', 0.0):.2f}")
                logger.info(f"[STATUS] Active Signals: {len(self.active_signals)}")
                logger.info("[STATUS] ═══════════════════════════════════════════════════════════════")

        except Exception as e:
            logger.error(f"[ERROR] Error logging system status: {e}")

    async def _cleanup_on_failure(self):
        """Cleanup resources on initialization failure"""
        try:
            logger.info("[CLEANUP] Cleaning up resources after initialization failure...")

            # Cleanup in reverse order of initialization
            if self.execution_worker_pool:
                await self.execution_worker_pool.shutdown()

            if self.performance_analysis_agent:
                if hasattr(self.performance_analysis_agent, 'shutdown'):
                    await self.performance_analysis_agent.shutdown()
                elif hasattr(self.performance_analysis_agent, 'stop'):
                    await self.performance_analysis_agent.stop()

            if self.risk_management_agent:
                await self.risk_management_agent.shutdown()

            if self.signal_generation_agent:
                if hasattr(self.signal_generation_agent, 'shutdown'):
                    await self.signal_generation_agent.shutdown()
                elif hasattr(self.signal_generation_agent, 'stop'):
                    await self.signal_generation_agent.stop()

            if self.market_monitoring_agent:
                await self.market_monitoring_agent.shutdown()

            if self.worker_state_manager:
                await self.worker_state_manager.shutdown()

            if self.communication_interface:
                await self.communication_interface.shutdown()

        except Exception as e:
            logger.error(f"[ERROR] Error during cleanup: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # PUBLIC API METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            base_status = {
                'orchestrator_running': self.is_running,
                'market_hours': self.is_market_hours,
                'system_start_time': self.system_start_time.isoformat() if self.system_start_time else None,
                'active_signals': len(self.active_signals),
                'system_metrics': self.system_metrics
            }

            # Add worker state manager status
            if self.worker_state_manager:
                worker_status = self.worker_state_manager.get_system_status()
                base_status.update(worker_status)

            # Add execution pool status
            if self.execution_worker_pool:
                pool_status = self.execution_worker_pool.get_pool_status()
                base_status['execution_pool'] = pool_status

            return base_status

        except Exception as e:
            logger.error(f"[ERROR] Error getting system status: {e}")
            return {'error': str(e)}

    async def emergency_shutdown(self) -> bool:
        """Emergency shutdown of the entire system"""
        try:
            logger.warning("[EMERGENCY] Initiating emergency shutdown...")

            # Emergency stop all trades first
            if self.execution_worker_pool:
                stop_results = await self.execution_worker_pool.emergency_stop_all_trades()
                logger.warning(f"[EMERGENCY] Trade stop results: {stop_results}")

            # Then perform normal shutdown
            await self.shutdown()

            logger.warning("[EMERGENCY] Emergency shutdown complete")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Emergency shutdown failed: {e}")
            return False

    async def force_worker_state_change(self, worker_id: str, new_state: str, reason: str = "Manual override") -> bool:
        """Force change worker state (admin function)"""
        try:
            if not self.worker_state_manager:
                return False

            from .execution.worker_state_manager import WorkerState
            worker_state = WorkerState(new_state.upper())

            return await self.worker_state_manager.force_worker_state_change(worker_id, worker_state, reason)

        except Exception as e:
            logger.error(f"[ERROR] Failed to force worker state change: {e}")
            return False

    def get_active_signals(self) -> Dict[str, Any]:
        """Get currently active signals"""
        return self.active_signals.copy()

    async def manual_signal_injection(self, signal_data: Dict[str, Any]) -> bool:
        """Manually inject a trading signal (for testing/admin purposes)"""
        try:
            logger.warning(f"[MANUAL] Manual signal injection: {signal_data}")

            # This would create a TradingSignal object and process it
            # Implementation depends on the signal data structure

            # For now, return success
            return True

        except Exception as e:
            logger.error(f"[ERROR] Manual signal injection failed: {e}")
            return False


# ═══════════════════════════════════════════════════════════════════════════════
# FACTORY FUNCTION
# ═══════════════════════════════════════════════════════════════════════════════

def create_live_trading_orchestrator(config: SystemConfiguration = None) -> LiveTradingOrchestrator:
    """Factory function to create LiveTradingOrchestrator instance"""
    return LiveTradingOrchestrator(config)


# ═══════════════════════════════════════════════════════════════════════════════
# MAIN FUNCTION FOR TESTING
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Test function for LiveTradingOrchestrator"""
    import logging
    logging.basicConfig(level=logging.INFO)

    # Create orchestrator
    orchestrator = LiveTradingOrchestrator()

    try:
        # Initialize system
        logger.info("Initializing live trading system...")
        if not await orchestrator.initialize():
            logger.error("Failed to initialize system")
            return

        # Start live trading
        logger.info("Starting live trading...")
        if not await orchestrator.start_live_trading():
            logger.error("Failed to start live trading")
            return

        # Run for a short time
        logger.info("System running... (will shutdown in 30 seconds)")
        await asyncio.sleep(30)

        # Shutdown
        await orchestrator.shutdown()
        logger.info("Test completed successfully")

    except Exception as e:
        logger.error(f"Test failed: {e}")
        await orchestrator.emergency_shutdown()


if __name__ == "__main__":
    asyncio.run(main())
