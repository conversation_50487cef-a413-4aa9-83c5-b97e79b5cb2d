analyse @/agents/run_strategy_evolution.py for following enhancement:

### 4. Consistent Default Fitness Metrics

__Current State:__

- The `_get_default_fitness_metrics` method returns all zeros, which is a very conservative default. However, `evaluate_strategy_fitness_batch` uses slightly different default metrics (e.g., `sharpe_ratio: 0.1`, `max_drawdown: 15.0`). This inconsistency can lead to unpredictable behavior when backtesting fails or no performance data is available.

__Proposed Improvement:__

- __Single Source of Truth:__ Establish a single, consistent set of default fitness metrics (e.g., a small positive composite score to give new strategies a chance) and use `_get_default_fitness_metrics` as the canonical source, applying it uniformly across all evaluation functions.

### 5. Enhanced Configurability

__Current State:__

- Several important parameters are hardcoded, reducing flexibility:

  - `max_concurrent_strategies` is fixed at 10 for both GPU and CPU.
  - Sleep intervals (`asyncio.sleep(60)` in infinite mode, `asyncio.sleep(3600)` in `start_evolution_process`) are hardcoded.
  - Default parameters in `_create_fast_variant` are hardcoded.
  - The Performance Analysis Gateway `callback_endpoint` is hardcoded to `http://localhost:8088/performance_prediction`.

__Proposed Improvement:__

- __Externalize Parameters:__ Move all hardcoded values to the `enhanced_strategy_evolution_config.yaml` file, allowing users to easily adjust these parameters without modifying the code. This includes concurrency limits, sleep durations, and integration endpoints.

### 6. Database Connection Management

__Current State:__

- Database connections are opened and closed explicitly (`sqlite3.connect`, `conn.close()`). While functional, this can be less robust in the face of exceptions.

__Proposed Improvement:__

- __Context Manager:__ Adopt the `with sqlite3.connect(...) as conn:` pattern for all database operations. This ensures connections are properly closed even if errors occur, improving resource management and preventing potential leaks.

### 7. Refactor `YAMLManager` Initialization

__Current State:__

- The `YAMLManager` is initialized twice in `__init__`: first with `None`, then with `self.evolution_config`. This is a bit awkward and could be streamlined.

__Proposed Improvement:__

- __Single, Streamlined Initialization:__ Refactor the initialization to load the raw configuration first, then create the `EvolutionConfig` object, and finally initialize `YAMLManager` once with the fully loaded `EvolutionConfig`. This would require a minor adjustment to `YAMLManager` to allow loading raw config without a full `EvolutionConfig` object initially.

### 8. Logging Consistency

__Current State:__

- While a custom `logger` is used extensively, some `print` statements are still present, particularly for immediate feedback during parallel processing.

__Proposed Improvement:__

- __Unified Logging:__ Replace all `print` statements with appropriate `logger.info` or `logger.debug` calls. This ensures all output is consistently formatted, timestamped, and routed through the logging system, making it easier to manage and analyze logs.


and break it in smaller task and modify code accordingly. make it modular and save new modules in agents/strategy_evolution.
there are too much import in starting so it may lag on startup use lazy load for faster and smooth startup.No synthetic data, DEMO data, Falaback to simmulation to be used. If it is change it use real data & logics and if it fails it should through relevant error.
